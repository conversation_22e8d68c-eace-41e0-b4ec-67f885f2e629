import requests
import pandas as pd
import datetime
import pytz

url = "https://api.dhan.co/charts/intraday"

payload = {
    "securityId": "62376",
    "exchangeSegment": "NSE_FNO",
    "instrument": "OPTIDX",
    "expiryCode": 1,
    "oi": False,
    "fromDate": "2025-06-18",
    "toDate": "2025-06-19"
}
headers = {
    "access-token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw",
    "Content-Type": "application/json",
    "Accept": "application/json"
}

response = requests.post(url, json=payload, headers=headers)
print("\nResponse keys:", list(response.json().keys()) if isinstance(response.json(), dict) else "Not a dictionary")

data = response.json()


# Create DataFrame from individual lists
df = pd.DataFrame({
    "open": data["open"],
    "high": data["high"],
    "low": data["low"],
    "close": data["close"],
    "volume": data["volume"],
    "timestamp": pd.to_datetime(data["start_Time"], unit="s", utc=True)
})

IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))
# Convert to IST and overwrite 'timestamp'
df["timestamp"] = df["timestamp"].dt.tz_convert(IST)

df.head(20)