#!/usr/bin/env python3
"""
Quick Test Script for DhanHQ Option Data Fetcher

This script performs a quick test of the system without making API calls.
Use this to verify the installation and basic functionality.
"""

import os
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Run quick test."""
    print("DhanHQ Option Data Fetcher - Quick Test")
    print("=" * 40)
    
    # Test 1: Check if .env file exists
    print("1. Checking environment file...")
    if os.path.exists('.env'):
        print("   ✓ .env file found")
    else:
        print("   ⚠ .env file not found. Copy .env.template to .env and configure it.")
    
    # Test 2: Check if CSV file exists
    print("2. Checking user options file...")
    if os.path.exists('User_options_input.csv'):
        with open('User_options_input.csv', 'r') as f:
            lines = f.readlines()
        print(f"   ✓ CSV file found with {len(lines)-1} option(s)")
    else:
        print("   ⚠ User_options_input.csv not found")
    
    # Test 3: Test imports
    print("3. Testing imports...")
    try:
        import src.config as config_module
        import src.instrument_manager as instrument_manager
        import src.historical_data as historical_data
        import src.websocket_client as websocket_client
        import src.data_processor as data_processor
        import src.error_handler as error_handler
        print("   ✓ All modules imported successfully")
    except ImportError as e:
        print(f"   ✗ Import error: {e}")
        return 1
    
    # Test 4: Test configuration (without API calls)
    print("4. Testing configuration...")
    try:
        config = config_module.get_config()
        print("   ✓ Configuration loaded")

        # Check if credentials are set (don't print them)
        if hasattr(config, 'client_id') and hasattr(config, 'access_token'):
            if config.client_id and config.access_token:
                print("   ✓ API credentials configured")
            else:
                print("   ⚠ API credentials not set in .env file")

    except Exception as e:
        print(f"   ✗ Configuration error: {e}")
        return 1

    # Test 5: Test option name parsing
    print("5. Testing option name parsing...")
    try:
        manager = instrument_manager.InstrumentManager(config)
        test_names = [
            "NIFTY 26 JUN 24500 CALL",
            "NIFTY 26 JUN 24500 PUT",
            "NIFTY 30 JUL 25000 CALL"
        ]
        
        for name in test_names:
            parsed = manager.parse_option_name(name)
            if parsed:
                print(f"   ✓ Parsed: {name}")
            else:
                print(f"   ⚠ Failed to parse: {name}")
    
    except Exception as e:
        print(f"   ✗ Parsing error: {e}")
        return 1
    
    # Test 6: Test data processor
    print("6. Testing data processor...")
    try:
        from datetime import datetime
        
        aggregator = data_processor.CandleAggregator(1)  # 1-minute candles

        # Test with sample data
        sample_tick = {
            'price': 100.0,
            'volume': 10,
            'timestamp': datetime.now().isoformat()
        }

        aggregator.add_tick(sample_tick)
        print("   ✓ Data processor working")
        
    except Exception as e:
        print(f"   ✗ Data processor error: {e}")
        return 1
    
    print("\n" + "=" * 40)
    print("Quick test completed!")
    print("\nNext steps:")
    print("1. Ensure your .env file has valid DhanHQ API credentials")
    print("2. Run full installation test: python test_installation.py")
    print("3. Start the application: python main.py --full")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
