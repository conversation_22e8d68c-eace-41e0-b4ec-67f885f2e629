# DhanHQ Option Data Fetcher and Real-time Monitor

A comprehensive Python application for fetching historical option data and monitoring real-time option prices from the DhanHQ v2 API. This tool focuses on NIFTY options and provides robust data collection, processing, and storage capabilities.

## Features

- **Historical Data Collection**: Fetch OHLC data for multiple timeframes (1min, 5min, 60min)
- **Real-time Data Streaming**: WebSocket-based live tick data monitoring
- **Instrument Management**: Automatic mapping of option names to instrument tokens
- **Data Processing**: Tick-to-candle aggregation with multiple timeframe support
- **Error Handling**: Comprehensive error handling with automatic recovery
- **Rate Limiting**: Built-in API rate limit detection and handling
- **Atomic File Operations**: Safe data storage with atomic file writes
- **Configurable Logging**: Detailed logging with configurable levels
- **CLI Interface**: Command-line interface for easy operation

## Installation

### Prerequisites

- Python 3.8 or higher
- DhanHQ trading account with API access
- Valid DhanHQ API credentials (Client ID and Access Token)

### Setup

1. **Clone or download the project**:
   ```bash
   cd "Option Trade_v1"
   ```

2. **Create and activate virtual environment**:
   ```bash
   # Create virtual environment
   uv venv .venv
   
   # Activate virtual environment
   # On Windows (Git Bash):
   source .venv/Scripts/activate
   # On Linux/Mac:
   source .venv/bin/activate
   ```

3. **Install dependencies**:
   ```bash
   uv pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   # Copy template and edit with your credentials
   cp .env.template .env
   ```

   Edit `.env` file with your DhanHQ credentials:
   ```env
   DHAN_CLIENT_ID=your_client_id_here
   DHAN_ACCESS_TOKEN=your_access_token_here
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required: DhanHQ API Credentials
DHAN_CLIENT_ID=your_client_id_here
DHAN_ACCESS_TOKEN=your_access_token_here

# Optional: Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/dhan_option_monitor.log

# Optional: Data Storage Configuration
DATA_DIR=data
HISTORICAL_DATA_DIR=data/historical
REALTIME_DATA_DIR=data/realtime
```

### User Options Input

Edit `User_options_input.csv` to specify which options to monitor:

```csv
UNDERLYING_SYMBOL,DISPLAY_NAME
NIFTY,NIFTY 26 JUN 24500 PUT
NIFTY,NIFTY 26 JUN 25100 CALL
```

**Format Requirements**:
- `UNDERLYING_SYMBOL`: The underlying asset (e.g., NIFTY)
- `DISPLAY_NAME`: Full option name in format "SYMBOL DD MMM STRIKE CALL/PUT"

## Usage

### Command Line Interface

The application provides several operation modes:

#### Full Workflow (Recommended for first run)
```bash
python main.py --full
```
This will:
1. Download and map instruments
2. Fetch historical data
3. Start real-time monitoring

#### Individual Operations

**Setup instruments only**:
```bash
python main.py --setup-only
```

**Fetch historical data only**:
```bash
python main.py --historical-only
```

**Start real-time monitoring only**:
```bash
python main.py --realtime-only
```

#### Advanced Options

**Use custom options file**:
```bash
python main.py --csv custom_options.csv --full
```

**Specify timeframes**:
```bash
python main.py --historical-only --intervals 1 5
```

**Force refresh instrument data**:
```bash
python main.py --setup-only --force-refresh
```

**Custom environment file**:
```bash
python main.py --env production.env --full
```

**Check system status**:
```bash
python main.py --status
```

### Output Files

The application creates the following file structure:

```
data/
├── instruments.csv              # Downloaded instrument list
├── mapped_instruments.json      # Mapped option instruments
├── historical/                  # Historical OHLC data
│   ├── NIFTY_26_JUN_24500_PUT_1min.csv
│   ├── NIFTY_26_JUN_24500_PUT_5min.csv
│   └── NIFTY_26_JUN_24500_PUT_60min.csv
└── realtime/                    # Real-time aggregated data
    ├── NIFTY_26_JUN_24500_PUT_1min_realtime.csv
    └── ...

logs/
├── dhan_option_monitor.log      # Application logs
└── errors.json                  # Error history
```

### CSV Data Format

All CSV files contain the following columns:

- `timestamp`: Date and time (ISO format)
- `open`: Opening price
- `high`: Highest price
- `low`: Lowest price
- `close`: Closing price
- `volume`: Trading volume

## API Endpoints Used

- **Instruments**: `https://api.dhan.co/v2/instruments`
- **Historical Data**: `https://api.dhan.co/v2/charts/intraday`
- **WebSocket**: `wss://api.dhan.co/v2/websocket`

## Error Handling

The application includes comprehensive error handling:

- **Rate Limiting**: Automatic detection and backoff
- **Connection Issues**: Exponential backoff reconnection
- **Authentication Errors**: Clear error messages and logging
- **Data Validation**: Input validation and error recovery
- **File Operations**: Atomic writes to prevent corruption

## Logging

Logs are written to both console and file with configurable levels:

- **DEBUG**: Detailed debugging information
- **INFO**: General operational information
- **WARNING**: Warning messages
- **ERROR**: Error conditions
- **CRITICAL**: Critical errors requiring attention

## Troubleshooting

### Common Issues

1. **Authentication Failed (401/403)**:
   - Verify your Client ID and Access Token in `.env`
   - Ensure your DhanHQ account has API access enabled
   - Check if tokens have expired

2. **Rate Limit Errors (429)**:
   - The application handles this automatically
   - Reduce the number of instruments if persistent
   - Check your API usage limits

3. **WebSocket Connection Issues**:
   - Check your internet connection
   - Verify firewall settings allow WebSocket connections
   - The application will automatically attempt reconnection

4. **No Data Retrieved**:
   - Verify option names in CSV match DhanHQ format exactly
   - Check if options are actively traded
   - Ensure market hours for data availability

5. **File Permission Errors**:
   - Ensure write permissions for data and logs directories
   - Check disk space availability

### Debug Mode

Run with debug logging for detailed information:

```bash
python main.py --log-level DEBUG --full
```

### Manual Testing

Test individual components:

```python
# Test configuration
from src.config import get_config
config = get_config()
print("Configuration loaded successfully")

# Test instrument mapping
from src.instrument_manager import InstrumentManager
manager = InstrumentManager(config)
manager.download_instruments()
manager.load_user_options()
instruments = manager.map_user_options()
print(f"Mapped {len(instruments)} instruments")
```

## Performance Considerations

- **Memory Usage**: The application keeps recent data in memory for fast access
- **Disk Space**: Historical data files can grow large over time
- **Network**: WebSocket maintains persistent connection
- **CPU**: Real-time processing is lightweight but continuous

## Security Notes

- Store API credentials securely in `.env` file
- Never commit `.env` file to version control
- Regularly rotate API tokens as per DhanHQ guidelines
- Monitor API usage to stay within limits

## Support

For issues related to:
- **DhanHQ API**: Contact DhanHQ support
- **Application bugs**: Check logs and error files for details
- **Feature requests**: Document requirements clearly

## License

This project is for educational and personal use. Ensure compliance with DhanHQ's API terms of service.
