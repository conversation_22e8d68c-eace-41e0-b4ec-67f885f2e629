"""
Data Processing and Aggregation Engine for DhanHQ Option Data Fetcher.

This module handles tick-to-candle aggregation, DataFrame management,
and atomic file writing for real-time data processing.
"""

import os
import time
import threading
import logging
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json

from .config import DhanConfig, LoggerSetup


class CandleAggregator:
    """Aggregates tick data into OHLC candles for different timeframes."""
    
    def __init__(self, timeframe_minutes: int):
        """
        Initialize candle aggregator.
        
        Args:
            timeframe_minutes (int): Timeframe in minutes (1, 5, 60)
        """
        self.timeframe_minutes = timeframe_minutes
        self.timeframe_seconds = timeframe_minutes * 60
        self.current_candle = None
        self.last_candle_time = None
        
    def add_tick(self, tick_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Add tick data and return completed candle if any.
        
        Args:
            tick_data (Dict[str, Any]): Tick data with price and timestamp
            
        Returns:
            Optional[Dict[str, Any]]: Completed candle or None
        """
        try:
            # Extract price and timestamp
            price = float(tick_data.get('price', tick_data.get('ltp', 0)))
            volume = int(tick_data.get('volume', tick_data.get('vol', 0)))
            
            # Parse timestamp
            timestamp_str = tick_data.get('timestamp', datetime.now().isoformat())
            if isinstance(timestamp_str, str):
                timestamp = pd.to_datetime(timestamp_str)
            else:
                timestamp = timestamp_str
            
            # Calculate candle start time
            candle_start = self._get_candle_start_time(timestamp)
            
            # Check if we need to start a new candle
            if self.current_candle is None or candle_start != self.last_candle_time:
                # Return completed candle if exists
                completed_candle = self.current_candle
                
                # Start new candle
                self.current_candle = {
                    'timestamp': candle_start,
                    'open': price,
                    'high': price,
                    'low': price,
                    'close': price,
                    'volume': volume,
                    'tick_count': 1
                }
                self.last_candle_time = candle_start
                
                return completed_candle
            else:
                # Update current candle
                self.current_candle['high'] = max(self.current_candle['high'], price)
                self.current_candle['low'] = min(self.current_candle['low'], price)
                self.current_candle['close'] = price
                self.current_candle['volume'] += volume
                self.current_candle['tick_count'] += 1
                
                return None
                
        except Exception as e:
            logging.error(f"Error processing tick data: {e}")
            return None
    
    def _get_candle_start_time(self, timestamp: datetime) -> datetime:
        """
        Get candle start time for given timestamp.
        
        Args:
            timestamp (datetime): Tick timestamp
            
        Returns:
            datetime: Candle start time
        """
        # Round down to the nearest timeframe boundary
        minutes = (timestamp.minute // self.timeframe_minutes) * self.timeframe_minutes
        return timestamp.replace(minute=minutes, second=0, microsecond=0)
    
    def get_current_candle(self) -> Optional[Dict[str, Any]]:
        """
        Get current incomplete candle.
        
        Returns:
            Optional[Dict[str, Any]]: Current candle or None
        """
        return self.current_candle.copy() if self.current_candle else None
    
    def force_complete_candle(self) -> Optional[Dict[str, Any]]:
        """
        Force complete current candle and return it.
        
        Returns:
            Optional[Dict[str, Any]]: Completed candle or None
        """
        if self.current_candle:
            completed_candle = self.current_candle
            self.current_candle = None
            self.last_candle_time = None
            return completed_candle
        return None


class DataProcessor:
    """Main data processor for handling real-time tick data and aggregation."""
    
    def __init__(self, config: DhanConfig):
        """
        Initialize data processor.
        
        Args:
            config (DhanConfig): Configuration instance
        """
        self.config = config
        self.logger = LoggerSetup.setup_logger(config, "data_processor")
        
        # Aggregators for different timeframes
        self.aggregators = {}
        for timeframe in config.SUPPORTED_TIMEFRAMES:
            self.aggregators[timeframe] = {}
        
        # Data storage
        self.dataframes = {}
        for timeframe in config.SUPPORTED_TIMEFRAMES:
            self.dataframes[timeframe] = {}
        
        # Threading
        self.processing_thread = None
        self.stop_event = threading.Event()
        self.data_queue = deque(maxlen=10000)
        self.lock = threading.Lock()
        
        # File writing
        self.last_save_time = {}
        self.save_interval = config.UPDATE_INTERVAL
        
        # Statistics
        self.stats = {
            'ticks_processed': 0,
            'candles_created': 0,
            'files_updated': 0,
            'errors': 0
        }
    
    def start_processing(self) -> None:
        """Start the data processing thread."""
        if self.processing_thread and self.processing_thread.is_alive():
            return
        
        self.stop_event.clear()
        self.processing_thread = threading.Thread(target=self._processing_loop)
        self.processing_thread.daemon = True
        self.processing_thread.start()
        
        self.logger.info("Data processing thread started")
    
    def stop_processing(self) -> None:
        """Stop the data processing thread."""
        self.stop_event.set()
        
        if self.processing_thread and self.processing_thread.is_alive():
            self.processing_thread.join(timeout=5)
        
        # Save any remaining data
        self._save_all_dataframes()
        
        self.logger.info("Data processing thread stopped")
    
    def add_tick_data(self, tick_data: Dict[str, Any]) -> None:
        """
        Add tick data to processing queue.
        
        Args:
            tick_data (Dict[str, Any]): Tick data
        """
        with self.lock:
            self.data_queue.append(tick_data)
    
    def _processing_loop(self) -> None:
        """Main processing loop."""
        self.logger.info("Starting data processing loop")
        
        while not self.stop_event.is_set():
            try:
                # Process queued tick data
                self._process_queued_ticks()
                
                # Check if we need to save data
                self._check_and_save_data()
                
                # Small sleep to prevent busy waiting
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Error in processing loop: {e}")
                self.stats['errors'] += 1
                time.sleep(1)
        
        self.logger.info("Data processing loop stopped")
    
    def _process_queued_ticks(self) -> None:
        """Process all queued tick data."""
        processed_count = 0
        
        while True:
            with self.lock:
                if not self.data_queue:
                    break
                tick_data = self.data_queue.popleft()
            
            try:
                self._process_single_tick(tick_data)
                processed_count += 1
                self.stats['ticks_processed'] += 1
                
            except Exception as e:
                self.logger.error(f"Error processing tick: {e}")
                self.stats['errors'] += 1
        
        if processed_count > 0:
            self.logger.debug(f"Processed {processed_count} ticks")
    
    def _process_single_tick(self, tick_data: Dict[str, Any]) -> None:
        """
        Process a single tick data point.
        
        Args:
            tick_data (Dict[str, Any]): Tick data
        """
        instrument_token = tick_data.get('instrument_token', tick_data.get('token'))
        if not instrument_token:
            return
        
        # Process for each timeframe
        for timeframe in self.config.SUPPORTED_TIMEFRAMES:
            # Get or create aggregator for this instrument and timeframe
            if instrument_token not in self.aggregators[timeframe]:
                self.aggregators[timeframe][instrument_token] = CandleAggregator(timeframe)
            
            aggregator = self.aggregators[timeframe][instrument_token]
            
            # Add tick to aggregator
            completed_candle = aggregator.add_tick(tick_data)
            
            if completed_candle:
                self._add_candle_to_dataframe(instrument_token, timeframe, completed_candle)
                self.stats['candles_created'] += 1
    
    def _add_candle_to_dataframe(
        self,
        instrument_token: str,
        timeframe: int,
        candle_data: Dict[str, Any]
    ) -> None:
        """
        Add completed candle to DataFrame.
        
        Args:
            instrument_token (str): Instrument token
            timeframe (int): Timeframe in minutes
            candle_data (Dict[str, Any]): Candle data
        """
        # Get or create DataFrame for this instrument and timeframe
        if instrument_token not in self.dataframes[timeframe]:
            self.dataframes[timeframe][instrument_token] = pd.DataFrame()
        
        df = self.dataframes[timeframe][instrument_token]
        
        # Create new row
        new_row = pd.DataFrame([candle_data])
        
        # Append to DataFrame
        if df.empty:
            self.dataframes[timeframe][instrument_token] = new_row
        else:
            # Check for duplicate timestamps
            if not df[df['timestamp'] == candle_data['timestamp']].empty:
                # Update existing candle
                mask = df['timestamp'] == candle_data['timestamp']
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in candle_data:
                        df.loc[mask, col] = candle_data[col]
            else:
                # Append new candle
                self.dataframes[timeframe][instrument_token] = pd.concat(
                    [df, new_row], ignore_index=True
                )
        
        # Keep only recent data (e.g., last 10000 candles)
        max_candles = 10000
        if len(self.dataframes[timeframe][instrument_token]) > max_candles:
            self.dataframes[timeframe][instrument_token] = \
                self.dataframes[timeframe][instrument_token].tail(max_candles).reset_index(drop=True)

    def _check_and_save_data(self) -> None:
        """Check if it's time to save data and save if needed."""
        current_time = time.time()

        for timeframe in self.config.SUPPORTED_TIMEFRAMES:
            for instrument_token in self.dataframes[timeframe]:
                key = f"{instrument_token}_{timeframe}"

                # Check if enough time has passed since last save
                if (key not in self.last_save_time or
                    current_time - self.last_save_time[key] >= self.save_interval):

                    self._save_dataframe(instrument_token, timeframe)
                    self.last_save_time[key] = current_time

    def _save_dataframe(self, instrument_token: str, timeframe: int) -> None:
        """
        Save DataFrame to CSV file atomically.

        Args:
            instrument_token (str): Instrument token
            timeframe (int): Timeframe in minutes
        """
        try:
            df = self.dataframes[timeframe].get(instrument_token)
            if df is None or df.empty:
                return

            # Create filename
            safe_token = self._create_safe_filename(instrument_token)
            filename = f"{safe_token}_{timeframe}min_realtime.csv"
            filepath = os.path.join(self.config.realtime_dir, filename)

            # Atomic write
            temp_filepath = filepath + ".tmp"
            df.to_csv(temp_filepath, index=False)

            # Atomic rename
            if os.path.exists(filepath):
                os.remove(filepath)
            os.rename(temp_filepath, filepath)

            self.stats['files_updated'] += 1
            self.logger.debug(f"Saved {len(df)} candles to {filepath}")

        except Exception as e:
            self.logger.error(f"Failed to save DataFrame for {instrument_token}: {e}")
            # Clean up temp file if it exists
            temp_filepath = filepath + ".tmp"
            if os.path.exists(temp_filepath):
                try:
                    os.remove(temp_filepath)
                except:
                    pass

    def _save_all_dataframes(self) -> None:
        """Save all DataFrames to files."""
        self.logger.info("Saving all DataFrames...")

        for timeframe in self.config.SUPPORTED_TIMEFRAMES:
            for instrument_token in self.dataframes[timeframe]:
                self._save_dataframe(instrument_token, timeframe)

        self.logger.info("All DataFrames saved")

    def _create_safe_filename(self, name: str) -> str:
        """
        Create a safe filename from instrument name.

        Args:
            name (str): Original name

        Returns:
            str: Safe filename
        """
        safe_name = "".join(c for c in name if c.isalnum() or c in "_-")
        return safe_name

    def get_dataframe(self, instrument_token: str, timeframe: int) -> Optional[pd.DataFrame]:
        """
        Get DataFrame for instrument and timeframe.

        Args:
            instrument_token (str): Instrument token
            timeframe (int): Timeframe in minutes

        Returns:
            Optional[pd.DataFrame]: DataFrame or None if not found
        """
        return self.dataframes.get(timeframe, {}).get(instrument_token)

    def get_latest_candle(self, instrument_token: str, timeframe: int) -> Optional[Dict[str, Any]]:
        """
        Get latest candle for instrument and timeframe.

        Args:
            instrument_token (str): Instrument token
            timeframe (int): Timeframe in minutes

        Returns:
            Optional[Dict[str, Any]]: Latest candle or None
        """
        df = self.get_dataframe(instrument_token, timeframe)
        if df is not None and not df.empty:
            return df.iloc[-1].to_dict()
        return None

    def get_current_candle(self, instrument_token: str, timeframe: int) -> Optional[Dict[str, Any]]:
        """
        Get current incomplete candle for instrument and timeframe.

        Args:
            instrument_token (str): Instrument token
            timeframe (int): Timeframe in minutes

        Returns:
            Optional[Dict[str, Any]]: Current candle or None
        """
        aggregator = self.aggregators.get(timeframe, {}).get(instrument_token)
        if aggregator:
            return aggregator.get_current_candle()
        return None

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get processing statistics.

        Returns:
            Dict[str, Any]: Statistics dictionary
        """
        stats = self.stats.copy()
        stats['active_instruments'] = {}
        stats['queue_size'] = len(self.data_queue)

        for timeframe in self.config.SUPPORTED_TIMEFRAMES:
            stats['active_instruments'][f'{timeframe}min'] = len(self.dataframes[timeframe])

        return stats

    def clear_old_data(self, days_to_keep: int = 7) -> None:
        """
        Clear old data from DataFrames to free memory.

        Args:
            days_to_keep (int): Number of days of data to keep
        """
        cutoff_time = datetime.now() - timedelta(days=days_to_keep)

        for timeframe in self.config.SUPPORTED_TIMEFRAMES:
            for instrument_token in list(self.dataframes[timeframe].keys()):
                df = self.dataframes[timeframe][instrument_token]

                if not df.empty and 'timestamp' in df.columns:
                    # Convert timestamp column to datetime if needed
                    df['timestamp'] = pd.to_datetime(df['timestamp'])

                    # Filter data
                    filtered_df = df[df['timestamp'] >= cutoff_time]

                    if len(filtered_df) < len(df):
                        self.dataframes[timeframe][instrument_token] = filtered_df.reset_index(drop=True)
                        self.logger.info(f"Cleared old data for {instrument_token} ({timeframe}min): "
                                       f"{len(df)} -> {len(filtered_df)} candles")

    def add_instrument(self, instrument_token: str, display_name: str = "") -> None:
        """
        Add a new instrument for processing.

        Args:
            instrument_token (str): Instrument token
            display_name (str): Display name for logging
        """
        for timeframe in self.config.SUPPORTED_TIMEFRAMES:
            if instrument_token not in self.aggregators[timeframe]:
                self.aggregators[timeframe][instrument_token] = CandleAggregator(timeframe)

            if instrument_token not in self.dataframes[timeframe]:
                self.dataframes[timeframe][instrument_token] = pd.DataFrame()

        self.logger.info(f"Added instrument for processing: {display_name} ({instrument_token})")

    def remove_instrument(self, instrument_token: str) -> None:
        """
        Remove an instrument from processing.

        Args:
            instrument_token (str): Instrument token
        """
        for timeframe in self.config.SUPPORTED_TIMEFRAMES:
            self.aggregators[timeframe].pop(instrument_token, None)
            self.dataframes[timeframe].pop(instrument_token, None)

        self.logger.info(f"Removed instrument from processing: {instrument_token}")
