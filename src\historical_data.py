"""
Historical Data Collection Module for DhanHQ Option Data Fetcher.

This module handles fetching historical OHLC data from DhanHQ API
with support for multiple timeframes and pagination.
"""

import os
import time
import logging
import requests
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json

from .config import <PERSON>han<PERSON>onfig, LoggerSetup


class RateLimiter:
    """Simple rate limiter for API requests."""
    
    def __init__(self, max_requests: int, window_seconds: int):
        """
        Initialize rate limiter.
        
        Args:
            max_requests (int): Maximum requests allowed in window
            window_seconds (int): Time window in seconds
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = []
    
    def wait_if_needed(self) -> None:
        """Wait if rate limit would be exceeded."""
        now = time.time()
        
        # Remove old requests outside the window
        self.requests = [req_time for req_time in self.requests if now - req_time < self.window_seconds]
        
        # Check if we need to wait
        if len(self.requests) >= self.max_requests:
            sleep_time = self.window_seconds - (now - self.requests[0]) + 1
            if sleep_time > 0:
                time.sleep(sleep_time)
                self.requests = []
        
        # Record this request
        self.requests.append(now)


class HistoricalDataFetcher:
    """Fetches and manages historical OHLC data from DhanHQ API."""
    
    def __init__(self, config: DhanConfig):
        """
        Initialize the historical data fetcher.
        
        Args:
            config (DhanConfig): Configuration instance
        """
        self.config = config
        self.logger = LoggerSetup.setup_logger(config, "historical_data")
        self.rate_limiter = RateLimiter(
            max_requests=config.API_RATE_LIMIT,
            window_seconds=config.RATE_LIMIT_WINDOW
        )
        
    def fetch_historical_data(
        self,
        instrument_token: str,
        interval: int,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
        max_records: int = 5000
    ) -> pd.DataFrame:
        """
        Fetch historical OHLC data for an instrument.
        
        Args:
            instrument_token (str): Instrument token/ID
            interval (int): Timeframe in minutes (1, 5, 60)
            from_date (Optional[datetime]): Start date (default: 30 days ago)
            to_date (Optional[datetime]): End date (default: now)
            max_records (int): Maximum records to fetch
            
        Returns:
            pd.DataFrame: Historical OHLC data
            
        Raises:
            ValueError: If interval is not supported
            requests.RequestException: If API request fails
        """
        if interval not in self.config.SUPPORTED_TIMEFRAMES:
            raise ValueError(f"Unsupported interval: {interval}. Supported: {self.config.SUPPORTED_TIMEFRAMES}")
        
        # Set default date range
        if to_date is None:
            to_date = datetime.now()
        if from_date is None:
            from_date = to_date - timedelta(days=30)
        
        self.logger.info(
            f"Fetching historical data for {instrument_token}, "
            f"interval: {interval}min, from: {from_date}, to: {to_date}"
        )
        
        all_data = []
        current_from = from_date
        
        while current_from < to_date and len(all_data) < max_records:
            # Calculate chunk end date (API might have limits)
            chunk_to = min(current_from + timedelta(days=7), to_date)
            
            try:
                # Rate limiting
                self.rate_limiter.wait_if_needed()
                
                # Prepare request parameters
                params = {
                    "instrumentId": instrument_token,
                    "interval": interval,
                    "fromDate": current_from.strftime("%Y-%m-%d"),
                    "toDate": chunk_to.strftime("%Y-%m-%d")
                }
                
                # Make API request
                response = requests.get(
                    self.config.HISTORICAL_DATA_URL,
                    headers=self.config.get_auth_headers(),
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 429:
                    self.logger.warning("Rate limit hit, waiting...")
                    time.sleep(60)
                    continue
                
                response.raise_for_status()
                data = response.json()
                
                # Process response data
                if isinstance(data, dict) and 'data' in data:
                    chunk_data = data['data']
                elif isinstance(data, list):
                    chunk_data = data
                else:
                    self.logger.warning(f"Unexpected data format for {instrument_token}")
                    break
                
                if chunk_data:
                    all_data.extend(chunk_data)
                    self.logger.debug(f"Fetched {len(chunk_data)} records for chunk")
                else:
                    self.logger.debug("No data in chunk")
                
                # Move to next chunk
                current_from = chunk_to + timedelta(minutes=interval)
                
                # Small delay to be respectful to API
                time.sleep(0.1)
                
            except requests.RequestException as e:
                self.logger.error(f"API request failed for {instrument_token}: {e}")
                if "401" in str(e) or "403" in str(e):
                    raise  # Re-raise auth errors
                # Continue with next chunk for other errors
                current_from = chunk_to + timedelta(minutes=interval)
                continue
            
            except Exception as e:
                self.logger.error(f"Unexpected error fetching data for {instrument_token}: {e}")
                break
        
        # Convert to DataFrame
        if all_data:
            df = pd.DataFrame(all_data)
            df = self._process_dataframe(df)
            self.logger.info(f"Fetched {len(df)} total records for {instrument_token}")
            return df
        else:
            self.logger.warning(f"No data fetched for {instrument_token}")
            return pd.DataFrame()
    
    def _process_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Process and clean the historical data DataFrame.
        
        Args:
            df (pd.DataFrame): Raw data DataFrame
            
        Returns:
            pd.DataFrame: Processed DataFrame
        """
        if df.empty:
            return df
        
        # Standardize column names
        column_mapping = {
            'timestamp': 'timestamp',
            'time': 'timestamp',
            'datetime': 'timestamp',
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'volume': 'volume',
            'vol': 'volume'
        }
        
        # Rename columns to standard format
        df_processed = df.copy()
        for old_col, new_col in column_mapping.items():
            if old_col in df_processed.columns:
                df_processed = df_processed.rename(columns={old_col: new_col})
        
        # Ensure required columns exist
        required_columns = ['timestamp', 'open', 'high', 'low', 'close']
        missing_columns = [col for col in required_columns if col not in df_processed.columns]
        
        if missing_columns:
            self.logger.error(f"Missing required columns: {missing_columns}")
            return pd.DataFrame()
        
        # Convert timestamp to datetime
        if 'timestamp' in df_processed.columns:
            df_processed['timestamp'] = pd.to_datetime(df_processed['timestamp'], errors='coerce')
        
        # Convert OHLC to numeric
        for col in ['open', 'high', 'low', 'close']:
            df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')
        
        # Convert volume to numeric (if exists)
        if 'volume' in df_processed.columns:
            df_processed['volume'] = pd.to_numeric(df_processed['volume'], errors='coerce')
        else:
            df_processed['volume'] = 0
        
        # Remove rows with invalid data
        df_processed = df_processed.dropna(subset=['timestamp', 'open', 'high', 'low', 'close'])
        
        # Sort by timestamp
        df_processed = df_processed.sort_values('timestamp').reset_index(drop=True)
        
        # Remove duplicates
        df_processed = df_processed.drop_duplicates(subset=['timestamp'], keep='last')
        
        return df_processed

    def save_historical_data(
        self,
        df: pd.DataFrame,
        display_name: str,
        interval: int
    ) -> str:
        """
        Save historical data to CSV file.

        Args:
            df (pd.DataFrame): Historical data DataFrame
            display_name (str): Option display name
            interval (int): Timeframe in minutes

        Returns:
            str: Path to saved file
        """
        if df.empty:
            self.logger.warning(f"No data to save for {display_name}")
            return ""

        # Create safe filename
        safe_name = self._create_safe_filename(display_name)
        filename = f"{safe_name}_{interval}min.csv"
        filepath = os.path.join(self.config.historical_dir, filename)

        try:
            # Save with atomic write
            temp_filepath = filepath + ".tmp"
            df.to_csv(temp_filepath, index=False)

            # Atomic rename
            if os.path.exists(filepath):
                os.remove(filepath)
            os.rename(temp_filepath, filepath)

            self.logger.info(f"Saved {len(df)} records to {filepath}")
            return filepath

        except Exception as e:
            self.logger.error(f"Failed to save data for {display_name}: {e}")
            # Clean up temp file if it exists
            if os.path.exists(temp_filepath):
                try:
                    os.remove(temp_filepath)
                except:
                    pass
            return ""

    def _create_safe_filename(self, display_name: str) -> str:
        """
        Create a safe filename from display name.

        Args:
            display_name (str): Option display name

        Returns:
            str: Safe filename
        """
        # Replace spaces and special characters
        safe_name = display_name.replace(" ", "_")
        safe_name = "".join(c for c in safe_name if c.isalnum() or c in "_-")
        return safe_name

    def fetch_and_save_all_instruments(
        self,
        mapped_instruments: List[Dict],
        intervals: Optional[List[int]] = None
    ) -> Dict[str, List[str]]:
        """
        Fetch and save historical data for all mapped instruments.

        Args:
            mapped_instruments (List[Dict]): List of mapped instruments
            intervals (Optional[List[int]]): Timeframes to fetch (default: all supported)

        Returns:
            Dict[str, List[str]]: Dictionary mapping display names to saved file paths
        """
        if intervals is None:
            intervals = self.config.SUPPORTED_TIMEFRAMES

        results = {}
        total_instruments = len(mapped_instruments)

        self.logger.info(f"Starting historical data fetch for {total_instruments} instruments")

        for i, instrument in enumerate(mapped_instruments, 1):
            display_name = instrument.get('display_name', '')
            instrument_token = instrument.get('instrument_token', '')

            if not instrument_token:
                self.logger.warning(f"No instrument token for {display_name}")
                continue

            self.logger.info(f"Processing {i}/{total_instruments}: {display_name}")

            instrument_files = []

            for interval in intervals:
                try:
                    # Fetch historical data
                    df = self.fetch_historical_data(instrument_token, interval)

                    if not df.empty:
                        # Save to file
                        filepath = self.save_historical_data(df, display_name, interval)
                        if filepath:
                            instrument_files.append(filepath)

                    # Small delay between requests
                    time.sleep(0.5)

                except Exception as e:
                    self.logger.error(f"Failed to fetch {interval}min data for {display_name}: {e}")
                    continue

            if instrument_files:
                results[display_name] = instrument_files

            # Progress update
            if i % 5 == 0:
                self.logger.info(f"Completed {i}/{total_instruments} instruments")

        self.logger.info(f"Historical data fetch completed. Processed {len(results)} instruments successfully.")
        return results

    def load_historical_data(self, display_name: str, interval: int) -> pd.DataFrame:
        """
        Load historical data from saved CSV file.

        Args:
            display_name (str): Option display name
            interval (int): Timeframe in minutes

        Returns:
            pd.DataFrame: Historical data or empty DataFrame if not found
        """
        safe_name = self._create_safe_filename(display_name)
        filename = f"{safe_name}_{interval}min.csv"
        filepath = os.path.join(self.config.historical_dir, filename)

        if not os.path.exists(filepath):
            self.logger.warning(f"Historical data file not found: {filepath}")
            return pd.DataFrame()

        try:
            df = pd.read_csv(filepath)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            self.logger.debug(f"Loaded {len(df)} records from {filepath}")
            return df

        except Exception as e:
            self.logger.error(f"Failed to load historical data from {filepath}: {e}")
            return pd.DataFrame()

    def update_historical_data(
        self,
        display_name: str,
        instrument_token: str,
        interval: int,
        days_back: int = 1
    ) -> bool:
        """
        Update historical data with recent records.

        Args:
            display_name (str): Option display name
            instrument_token (str): Instrument token
            interval (int): Timeframe in minutes
            days_back (int): Number of days to fetch for update

        Returns:
            bool: True if update successful, False otherwise
        """
        try:
            # Load existing data
            existing_df = self.load_historical_data(display_name, interval)

            # Determine fetch range
            to_date = datetime.now()
            if not existing_df.empty:
                last_timestamp = existing_df['timestamp'].max()
                from_date = max(last_timestamp, to_date - timedelta(days=days_back))
            else:
                from_date = to_date - timedelta(days=days_back)

            # Fetch new data
            new_df = self.fetch_historical_data(
                instrument_token, interval, from_date, to_date
            )

            if new_df.empty:
                self.logger.debug(f"No new data for {display_name}")
                return True

            # Combine with existing data
            if not existing_df.empty:
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                combined_df = combined_df.drop_duplicates(subset=['timestamp'], keep='last')
                combined_df = combined_df.sort_values('timestamp').reset_index(drop=True)
            else:
                combined_df = new_df

            # Save updated data
            filepath = self.save_historical_data(combined_df, display_name, interval)

            if filepath:
                self.logger.info(f"Updated historical data for {display_name} ({interval}min)")
                return True
            else:
                return False

        except Exception as e:
            self.logger.error(f"Failed to update historical data for {display_name}: {e}")
            return False
