"""
Error Handling and Logging System for DhanHQ Option Data Fetcher.

This module provides comprehensive error handling, rate limit detection,
and detailed logging capabilities.
"""

import os
import time
import logging
import traceback
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from functools import wraps
from enum import Enum
import requests

from .config import DhanConfig, LoggerSetup


class ErrorType(Enum):
    """Enumeration of error types."""
    AUTHENTICATION = "authentication"
    RATE_LIMIT = "rate_limit"
    NETWORK = "network"
    API_ERROR = "api_error"
    DATA_PROCESSING = "data_processing"
    FILE_IO = "file_io"
    WEBSOCKET = "websocket"
    CONFIGURATION = "configuration"
    UNKNOWN = "unknown"


class ErrorSeverity(Enum):
    """Enumeration of error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorRecord:
    """Represents an error record with context and metadata."""
    
    def __init__(
        self,
        error_type: ErrorType,
        severity: ErrorSeverity,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None
    ):
        """
        Initialize error record.
        
        Args:
            error_type (ErrorType): Type of error
            severity (ErrorSeverity): Severity level
            message (str): Error message
            context (Optional[Dict[str, Any]]): Additional context
            exception (Optional[Exception]): Original exception if any
        """
        self.timestamp = datetime.now()
        self.error_type = error_type
        self.severity = severity
        self.message = message
        self.context = context or {}
        self.exception = exception
        self.traceback = traceback.format_exc() if exception else None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error record to dictionary."""
        return {
            'timestamp': self.timestamp.isoformat(),
            'error_type': self.error_type.value,
            'severity': self.severity.value,
            'message': self.message,
            'context': self.context,
            'exception_type': type(self.exception).__name__ if self.exception else None,
            'traceback': self.traceback
        }


class ErrorHandler:
    """Comprehensive error handler with logging and recovery mechanisms."""
    
    def __init__(self, config: DhanConfig):
        """
        Initialize error handler.
        
        Args:
            config (DhanConfig): Configuration instance
        """
        self.config = config
        self.logger = LoggerSetup.setup_logger(config, "error_handler")
        
        # Error tracking
        self.error_history = []
        self.error_counts = {}
        self.last_error_time = {}
        
        # Rate limiting tracking
        self.rate_limit_history = []
        self.rate_limit_reset_time = None
        
        # Recovery callbacks
        self.recovery_callbacks = {}
        
        # Error log file
        self.error_log_file = os.path.join(config.log_dir, "errors.json")
    
    def handle_error(
        self,
        error_type: ErrorType,
        severity: ErrorSeverity,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None,
        should_retry: bool = False
    ) -> bool:
        """
        Handle an error with logging and potential recovery.
        
        Args:
            error_type (ErrorType): Type of error
            severity (ErrorSeverity): Severity level
            message (str): Error message
            context (Optional[Dict[str, Any]]): Additional context
            exception (Optional[Exception]): Original exception
            should_retry (bool): Whether operation should be retried
            
        Returns:
            bool: True if error was handled successfully, False otherwise
        """
        # Create error record
        error_record = ErrorRecord(error_type, severity, message, context, exception)
        
        # Log error
        self._log_error(error_record)
        
        # Track error
        self._track_error(error_record)
        
        # Save to error log file
        self._save_error_to_file(error_record)
        
        # Attempt recovery if callback exists
        recovery_success = self._attempt_recovery(error_type, error_record)
        
        return recovery_success
    
    def _log_error(self, error_record: ErrorRecord) -> None:
        """Log error with appropriate level."""
        log_message = f"[{error_record.error_type.value.upper()}] {error_record.message}"
        
        if error_record.context:
            log_message += f" | Context: {error_record.context}"
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message, exc_info=error_record.exception)
        elif error_record.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message, exc_info=error_record.exception)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
    
    def _track_error(self, error_record: ErrorRecord) -> None:
        """Track error for pattern analysis."""
        # Add to history
        self.error_history.append(error_record)
        
        # Keep only recent errors (last 1000)
        if len(self.error_history) > 1000:
            self.error_history = self.error_history[-1000:]
        
        # Update error counts
        error_key = error_record.error_type.value
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        self.last_error_time[error_key] = error_record.timestamp
    
    def _save_error_to_file(self, error_record: ErrorRecord) -> None:
        """Save error record to JSON file."""
        try:
            # Load existing errors
            errors = []
            if os.path.exists(self.error_log_file):
                with open(self.error_log_file, 'r', encoding='utf-8') as f:
                    errors = json.load(f)
            
            # Add new error
            errors.append(error_record.to_dict())
            
            # Keep only recent errors (last 500)
            if len(errors) > 500:
                errors = errors[-500:]
            
            # Save back to file
            with open(self.error_log_file, 'w', encoding='utf-8') as f:
                json.dump(errors, f, indent=2, default=str)
                
        except Exception as e:
            self.logger.error(f"Failed to save error to file: {e}")
    
    def _attempt_recovery(self, error_type: ErrorType, error_record: ErrorRecord) -> bool:
        """Attempt error recovery using registered callbacks."""
        callback = self.recovery_callbacks.get(error_type)
        if callback:
            try:
                return callback(error_record)
            except Exception as e:
                self.logger.error(f"Recovery callback failed for {error_type.value}: {e}")
        return False
    
    def register_recovery_callback(self, error_type: ErrorType, callback: Callable) -> None:
        """
        Register a recovery callback for specific error type.
        
        Args:
            error_type (ErrorType): Error type to handle
            callback (Callable): Recovery function
        """
        self.recovery_callbacks[error_type] = callback
        self.logger.info(f"Registered recovery callback for {error_type.value}")
    
    def is_rate_limited(self) -> bool:
        """
        Check if we are currently rate limited.
        
        Returns:
            bool: True if rate limited, False otherwise
        """
        if self.rate_limit_reset_time:
            return datetime.now() < self.rate_limit_reset_time
        return False
    
    def handle_rate_limit(self, reset_time: Optional[datetime] = None) -> None:
        """
        Handle rate limit error.
        
        Args:
            reset_time (Optional[datetime]): When rate limit resets
        """
        if reset_time:
            self.rate_limit_reset_time = reset_time
        else:
            # Default to 1 minute from now
            self.rate_limit_reset_time = datetime.now() + timedelta(minutes=1)
        
        self.rate_limit_history.append(datetime.now())
        
        self.handle_error(
            ErrorType.RATE_LIMIT,
            ErrorSeverity.MEDIUM,
            f"Rate limit hit. Reset time: {self.rate_limit_reset_time}",
            context={'reset_time': self.rate_limit_reset_time.isoformat()}
        )
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """
        Get error statistics.
        
        Returns:
            Dict[str, Any]: Error statistics
        """
        now = datetime.now()
        last_hour = now - timedelta(hours=1)
        last_day = now - timedelta(days=1)
        
        # Count errors in different time windows
        errors_last_hour = [e for e in self.error_history if e.timestamp >= last_hour]
        errors_last_day = [e for e in self.error_history if e.timestamp >= last_day]
        
        return {
            'total_errors': len(self.error_history),
            'errors_last_hour': len(errors_last_hour),
            'errors_last_day': len(errors_last_day),
            'error_counts_by_type': self.error_counts.copy(),
            'last_error_times': {k: v.isoformat() for k, v in self.last_error_time.items()},
            'rate_limit_hits': len(self.rate_limit_history),
            'is_rate_limited': self.is_rate_limited(),
            'rate_limit_reset_time': self.rate_limit_reset_time.isoformat() if self.rate_limit_reset_time else None
        }
    
    def clear_old_errors(self, days_to_keep: int = 7) -> None:
        """
        Clear old error records.
        
        Args:
            days_to_keep (int): Number of days to keep
        """
        cutoff_time = datetime.now() - timedelta(days=days_to_keep)
        
        # Filter error history
        self.error_history = [e for e in self.error_history if e.timestamp >= cutoff_time]
        
        # Filter rate limit history
        self.rate_limit_history = [t for t in self.rate_limit_history if t >= cutoff_time]
        
        self.logger.info(f"Cleared error records older than {days_to_keep} days")


def retry_on_error(
    max_retries: int = 3,
    delay: float = 1.0,
    backoff_factor: float = 2.0,
    exceptions: tuple = (Exception,)
):
    """
    Decorator for retrying functions on error with exponential backoff.
    
    Args:
        max_retries (int): Maximum number of retries
        delay (float): Initial delay between retries
        backoff_factor (float): Backoff multiplier
        exceptions (tuple): Exception types to retry on
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        break
                    
                    wait_time = delay * (backoff_factor ** attempt)
                    time.sleep(wait_time)
            
            # If we get here, all retries failed
            raise last_exception
        
        return wrapper
    return decorator


def handle_api_response(response: requests.Response, error_handler: ErrorHandler) -> Dict[str, Any]:
    """
    Handle API response with proper error checking.
    
    Args:
        response (requests.Response): API response
        error_handler (ErrorHandler): Error handler instance
        
    Returns:
        Dict[str, Any]: Response data
        
    Raises:
        requests.RequestException: If response indicates error
    """
    try:
        # Check for rate limiting
        if response.status_code == 429:
            reset_time = None
            if 'X-RateLimit-Reset' in response.headers:
                try:
                    reset_timestamp = int(response.headers['X-RateLimit-Reset'])
                    reset_time = datetime.fromtimestamp(reset_timestamp)
                except (ValueError, TypeError):
                    pass
            
            error_handler.handle_rate_limit(reset_time)
            response.raise_for_status()
        
        # Check for authentication errors
        elif response.status_code in [401, 403]:
            error_handler.handle_error(
                ErrorType.AUTHENTICATION,
                ErrorSeverity.HIGH,
                f"Authentication failed: {response.status_code}",
                context={'status_code': response.status_code, 'response': response.text}
            )
            response.raise_for_status()
        
        # Check for other client errors
        elif 400 <= response.status_code < 500:
            error_handler.handle_error(
                ErrorType.API_ERROR,
                ErrorSeverity.MEDIUM,
                f"Client error: {response.status_code}",
                context={'status_code': response.status_code, 'response': response.text}
            )
            response.raise_for_status()
        
        # Check for server errors
        elif response.status_code >= 500:
            error_handler.handle_error(
                ErrorType.API_ERROR,
                ErrorSeverity.HIGH,
                f"Server error: {response.status_code}",
                context={'status_code': response.status_code, 'response': response.text}
            )
            response.raise_for_status()
        
        # Success - return JSON data
        return response.json()
        
    except requests.RequestException:
        raise
    except Exception as e:
        error_handler.handle_error(
            ErrorType.API_ERROR,
            ErrorSeverity.MEDIUM,
            f"Failed to parse API response: {e}",
            context={'response_text': response.text},
            exception=e
        )
        raise
