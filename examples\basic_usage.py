#!/usr/bin/env python3
"""
Basic Usage Example for DhanHQ Option Data Fetcher

This script demonstrates how to use the DhanHQ Option Data Fetcher
programmatically for custom applications.
"""

import os
import sys
import time
from datetime import datetime, timedelta

# Add parent src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from config import get_config
from instrument_manager import InstrumentManager
from historical_data import HistoricalDataFetcher
from websocket_client import <PERSON>hanWebSocketClient
from data_processor import DataProcessor
from error_handler import <PERSON>rror<PERSON>and<PERSON>


def example_1_basic_setup():
    """Example 1: Basic setup and instrument mapping."""
    print("=== Example 1: Basic Setup ===")
    
    # Load configuration
    config = get_config()
    
    # Initialize instrument manager
    manager = InstrumentManager(config)
    
    # Download instruments (cached for 24 hours)
    print("Downloading instruments...")
    instruments_df = manager.download_instruments()
    print(f"Downloaded {len(instruments_df)} instruments")
    
    # Load user options
    print("Loading user options...")
    user_options = manager.load_user_options("../User_options_input.csv")
    print(f"Loaded {len(user_options)} user options")
    
    # Map instruments
    print("Mapping instruments...")
    mapped_instruments = manager.map_user_options()
    print(f"Successfully mapped {len(mapped_instruments)} instruments")
    
    # Display mapped instruments
    for instrument in mapped_instruments:
        print(f"  - {instrument['display_name']} -> {instrument['instrument_token']}")
    
    return mapped_instruments


def example_2_historical_data(mapped_instruments):
    """Example 2: Fetch historical data."""
    print("\n=== Example 2: Historical Data ===")
    
    if not mapped_instruments:
        print("No mapped instruments available")
        return
    
    config = get_config()
    fetcher = HistoricalDataFetcher(config)
    
    # Fetch data for first instrument
    instrument = mapped_instruments[0]
    print(f"Fetching historical data for: {instrument['display_name']}")
    
    # Fetch 1-minute data for last 7 days
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    df = fetcher.fetch_historical_data(
        instrument['instrument_token'],
        interval=1,  # 1-minute
        from_date=start_date,
        to_date=end_date
    )
    
    if not df.empty:
        print(f"Fetched {len(df)} records")
        print("Sample data:")
        print(df.head())
        
        # Save to file
        filepath = fetcher.save_historical_data(df, instrument['display_name'], 1)
        print(f"Data saved to: {filepath}")
    else:
        print("No data retrieved")


def example_3_realtime_monitoring(mapped_instruments):
    """Example 3: Real-time data monitoring."""
    print("\n=== Example 3: Real-time Monitoring ===")
    
    if not mapped_instruments:
        print("No mapped instruments available")
        return
    
    config = get_config()
    
    # Initialize components
    data_processor = DataProcessor(config)
    error_handler = ErrorHandler(config)
    
    # Tick data handler
    def handle_tick(tick_data):
        print(f"Received tick: {tick_data}")
        data_processor.add_tick_data(tick_data)
    
    # Initialize WebSocket client
    ws_client = DhanWebSocketClient(config, on_tick=handle_tick)
    
    try:
        # Start data processor
        data_processor.start_processing()
        
        # Add instruments to processor
        for instrument in mapped_instruments:
            data_processor.add_instrument(
                instrument['instrument_token'],
                instrument['display_name']
            )
        
        # Connect to WebSocket
        print("Connecting to WebSocket...")
        if ws_client.connect():
            print("Connected successfully")
            
            # Subscribe to instruments
            instrument_tokens = [inst['instrument_token'] for inst in mapped_instruments]
            if ws_client.subscribe(instrument_tokens):
                print(f"Subscribed to {len(instrument_tokens)} instruments")
                
                # Monitor for 30 seconds
                print("Monitoring for 30 seconds...")
                start_time = time.time()
                
                while time.time() - start_time < 30:
                    # Check connection status
                    if not ws_client.is_connection_alive():
                        print("Connection lost")
                        break
                    
                    # Display statistics
                    stats = data_processor.get_statistics()
                    print(f"Ticks processed: {stats['ticks_processed']}, "
                          f"Candles created: {stats['candles_created']}")
                    
                    time.sleep(5)
                
                print("Monitoring completed")
            else:
                print("Failed to subscribe to instruments")
        else:
            print("Failed to connect to WebSocket")
    
    except KeyboardInterrupt:
        print("Monitoring interrupted")
    
    finally:
        # Cleanup
        ws_client.disconnect()
        data_processor.stop_processing()


def example_4_data_analysis(mapped_instruments):
    """Example 4: Analyze collected data."""
    print("\n=== Example 4: Data Analysis ===")
    
    if not mapped_instruments:
        print("No mapped instruments available")
        return
    
    config = get_config()
    fetcher = HistoricalDataFetcher(config)
    
    # Load historical data for analysis
    instrument = mapped_instruments[0]
    df = fetcher.load_historical_data(instrument['display_name'], 1)
    
    if df.empty:
        print("No historical data available for analysis")
        return
    
    print(f"Analyzing data for: {instrument['display_name']}")
    print(f"Data points: {len(df)}")
    print(f"Date range: {df['timestamp'].min()} to {df['timestamp'].max()}")
    
    # Basic statistics
    print("\nPrice Statistics:")
    print(f"  Highest price: {df['high'].max():.2f}")
    print(f"  Lowest price: {df['low'].min():.2f}")
    print(f"  Average close: {df['close'].mean():.2f}")
    print(f"  Total volume: {df['volume'].sum():,}")
    
    # Calculate simple moving averages
    df['sma_10'] = df['close'].rolling(window=10).mean()
    df['sma_20'] = df['close'].rolling(window=20).mean()
    
    print("\nTechnical Indicators:")
    print(f"  Current SMA(10): {df['sma_10'].iloc[-1]:.2f}")
    print(f"  Current SMA(20): {df['sma_20'].iloc[-1]:.2f}")
    
    # Price change analysis
    df['price_change'] = df['close'].pct_change()
    positive_changes = (df['price_change'] > 0).sum()
    negative_changes = (df['price_change'] < 0).sum()
    
    print(f"\nPrice Movement:")
    print(f"  Positive changes: {positive_changes}")
    print(f"  Negative changes: {negative_changes}")
    print(f"  Largest gain: {df['price_change'].max():.4f} ({df['price_change'].max()*100:.2f}%)")
    print(f"  Largest loss: {df['price_change'].min():.4f} ({df['price_change'].min()*100:.2f}%)")


def main():
    """Run all examples."""
    print("DhanHQ Option Data Fetcher - Usage Examples")
    print("=" * 50)
    
    try:
        # Example 1: Setup
        mapped_instruments = example_1_basic_setup()
        
        if mapped_instruments:
            # Example 2: Historical data
            example_2_historical_data(mapped_instruments)
            
            # Example 3: Real-time monitoring (commented out for safety)
            # Uncomment the next line to test real-time monitoring
            # example_3_realtime_monitoring(mapped_instruments)
            
            # Example 4: Data analysis
            example_4_data_analysis(mapped_instruments)
        
        print("\n" + "=" * 50)
        print("Examples completed successfully!")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
