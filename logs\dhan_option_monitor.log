2025-06-19 00:20:10,251 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:20:10,251 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:20:10,251 - instrument_manager - INFO - download_instruments:62 - Downloading instruments from DhanHQ API
2025-06-19 00:20:10,602 - instrument_manager - ERROR - download_instruments:87 - Failed to download instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:20:10,603 - error_handler - ERROR - _log_error:163 - [CONFIGURATION] Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\main.py", line 80, in setup_instruments
    self.instrument_manager.download_instruments(force_refresh)
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 70, in download_instruments
    response.raise_for_status()
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\requests\models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:22:09,400 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:22:09,401 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:22:09,401 - instrument_manager - INFO - download_instruments:62 - Downloading instruments from DhanHQ API
2025-06-19 00:22:09,823 - instrument_manager - ERROR - download_instruments:87 - Failed to download instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:22:09,826 - error_handler - ERROR - _log_error:163 - [CONFIGURATION] Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\main.py", line 80, in setup_instruments
    self.instrument_manager.download_instruments(force_refresh)
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 70, in download_instruments
    response.raise_for_status()
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\requests\models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:26:31,726 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:26:31,726 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:26:31,726 - instrument_manager - INFO - download_instruments:62 - Downloading instruments from DhanHQ API
2025-06-19 00:26:32,099 - instrument_manager - ERROR - download_instruments:87 - Failed to download instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:26:32,100 - error_handler - ERROR - _log_error:163 - [CONFIGURATION] Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\main.py", line 80, in setup_instruments
    self.instrument_manager.download_instruments(force_refresh)
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 70, in download_instruments
    response.raise_for_status()
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\requests\models.py", line 1021, in raise_for_status
    raise HTTPError(http_error_msg, response=self)
requests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments
2025-06-19 00:31:19,084 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:31:19,084 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:31:19,084 - instrument_manager - INFO - download_instruments:62 - Downloading instruments from DhanHQ CSV endpoint
2025-06-19 00:31:25,532 - instrument_manager - INFO - download_instruments:88 - Downloaded 180493 instruments from CSV
2025-06-19 00:31:25,535 - instrument_manager - INFO - load_user_options:116 - Loading user options from User_options_input.csv
2025-06-19 00:31:25,535 - instrument_manager - INFO - load_user_options:123 - Loaded 2 user options
2025-06-19 00:31:25,535 - instrument_manager - INFO - map_user_options:244 - Mapping user options to instruments
2025-06-19 00:31:25,539 - error_handler - ERROR - _log_error:163 - [CONFIGURATION] Failed to setup instruments: 'SEM_SMST_SECURITY_ID'
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\pandas\core\indexes\base.py", line 3790, in get_loc
    return self._engine.get_loc(casted_key)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "index.pyx", line 152, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 181, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\_libs\hashtable_class_helper.pxi", line 7080, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\_libs\hashtable_class_helper.pxi", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'SEM_SMST_SECURITY_ID'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\main.py", line 86, in setup_instruments
    self.mapped_instruments = self.instrument_manager.map_user_options()
                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 260, in map_user_options
    instrument = self.find_matching_instrument(parsed_option)
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\src\instrument_manager.py", line 193, in find_matching_instrument
    symbol_filter = self.instruments_df['SEM_SMST_SECURITY_ID'].str.contains(
                    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\pandas\core\frame.py", line 3896, in __getitem__
    indexer = self.columns.get_loc(key)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\Trading\India trading strategies\Option Trade_v1\.venv\Lib\site-packages\pandas\core\indexes\base.py", line 3797, in get_loc
    raise KeyError(key) from err
KeyError: 'SEM_SMST_SECURITY_ID'
2025-06-19 00:32:55,690 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:32:55,690 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:32:55,691 - instrument_manager - INFO - download_instruments:58 - Loading instruments from cached file
2025-06-19 00:32:56,223 - instrument_manager - INFO - load_user_options:116 - Loading user options from User_options_input.csv
2025-06-19 00:32:56,224 - instrument_manager - INFO - load_user_options:123 - Loaded 2 user options
2025-06-19 00:32:56,224 - instrument_manager - INFO - map_user_options:242 - Mapping user options to instruments
2025-06-19 00:32:56,276 - instrument_manager - INFO - find_matching_instrument:225 - Found matching instrument for NIFTY 26 JUN 24500 PUT: FINNIFTY 26 JUN 24500 PUT
2025-06-19 00:32:56,327 - instrument_manager - INFO - find_matching_instrument:225 - Found matching instrument for NIFTY 26 JUN 25100 CALL: FINNIFTY 26 JUN 25100 CALL
2025-06-19 00:32:56,327 - instrument_manager - INFO - map_user_options:280 - Successfully mapped 2 instruments
2025-06-19 00:32:56,331 - instrument_manager - INFO - _save_mapped_instruments:295 - Saved mapped instruments to data\mapped_instruments.json
2025-06-19 00:32:56,332 - main - INFO - setup_instruments:92 - Successfully mapped 2 instruments
2025-06-19 00:33:39,333 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:33:39,333 - main - INFO - setup_instruments:77 - Setting up instruments...
2025-06-19 00:33:39,334 - instrument_manager - INFO - download_instruments:58 - Loading instruments from cached file
2025-06-19 00:33:39,843 - instrument_manager - INFO - load_user_options:116 - Loading user options from User_options_input.csv
2025-06-19 00:33:39,843 - instrument_manager - INFO - load_user_options:123 - Loaded 2 user options
2025-06-19 00:33:39,843 - instrument_manager - INFO - map_user_options:240 - Mapping user options to instruments
2025-06-19 00:33:39,858 - instrument_manager - INFO - find_matching_instrument:223 - Found matching instrument for NIFTY 26 JUN 24500 PUT: NIFTY 26 JUN 24500 PUT
2025-06-19 00:33:39,872 - instrument_manager - INFO - find_matching_instrument:223 - Found matching instrument for NIFTY 26 JUN 25100 CALL: NIFTY 26 JUN 25100 CALL
2025-06-19 00:33:39,872 - instrument_manager - INFO - map_user_options:278 - Successfully mapped 2 instruments
2025-06-19 00:33:39,873 - instrument_manager - INFO - _save_mapped_instruments:293 - Saved mapped instruments to data\mapped_instruments.json
2025-06-19 00:33:39,873 - main - INFO - setup_instruments:92 - Successfully mapped 2 instruments
2025-06-19 00:33:56,246 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:33:56,247 - main - INFO - fetch_historical_data:115 - Starting historical data fetch...
2025-06-19 00:33:56,247 - main - ERROR - fetch_historical_data:118 - No mapped instruments. Run setup_instruments() first.
2025-06-19 00:34:33,630 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:34:33,630 - main - INFO - fetch_historical_data:140 - Starting historical data fetch...
2025-06-19 00:34:33,630 - main - INFO - _load_mapped_instruments:78 - Loaded 2 mapped instruments from file
2025-06-19 00:34:33,631 - historical_data - INFO - fetch_and_save_all_instruments:337 - Starting historical data fetch for 2 instruments
2025-06-19 00:34:33,631 - historical_data - INFO - fetch_and_save_all_instruments:347 - Processing 1/2: NIFTY 26 JUN 24500 PUT
2025-06-19 00:34:33,631 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62376, interval: 1min, from: 2025-05-20 00:34:33.631894, to: 2025-06-19 00:34:33.631894
2025-06-19 00:34:34,015 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:34:34,316 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-05-27&toDate=2025-06-03
2025-06-19 00:34:34,623 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-06-03&toDate=2025-06-10
2025-06-19 00:34:34,937 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-06-10&toDate=2025-06-17
2025-06-19 00:34:35,238 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-06-17&toDate=2025-06-19
2025-06-19 00:34:35,238 - historical_data - WARNING - fetch_historical_data:184 - No data fetched for 62376
2025-06-19 00:34:35,740 - historical_data - INFO - fetch_and_save_all_instruments:347 - Processing 2/2: NIFTY 26 JUN 25100 CALL
2025-06-19 00:34:35,740 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62401, interval: 1min, from: 2025-05-20 00:34:35.740976, to: 2025-06-19 00:34:35.740976
2025-06-19 00:34:36,040 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62401: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=1&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:34:36,040 - historical_data - ERROR - fetch_and_save_all_instruments:366 - Failed to fetch 1min data for NIFTY 26 JUN 25100 CALL: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=1&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:34:36,041 - historical_data - INFO - fetch_and_save_all_instruments:376 - Historical data fetch completed. Processed 0 instruments successfully.
2025-06-19 00:34:36,041 - main - INFO - fetch_historical_data:157 - Historical data fetch completed: 0/2 instruments
2025-06-19 00:34:47,764 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:38:23,440 - main - INFO - __init__:58 - DhanHQ Option Monitor initialized
2025-06-19 00:38:23,441 - main - INFO - setup_instruments:102 - Setting up instruments...
2025-06-19 00:38:23,441 - instrument_manager - INFO - download_instruments:58 - Loading instruments from cached file
2025-06-19 00:38:23,931 - instrument_manager - INFO - load_user_options:116 - Loading user options from User_options_input.csv
2025-06-19 00:38:23,932 - instrument_manager - INFO - load_user_options:123 - Loaded 2 user options
2025-06-19 00:38:23,932 - instrument_manager - INFO - map_user_options:240 - Mapping user options to instruments
2025-06-19 00:38:23,946 - instrument_manager - INFO - find_matching_instrument:223 - Found matching instrument for NIFTY 26 JUN 24500 PUT: NIFTY 26 JUN 24500 PUT
2025-06-19 00:38:23,962 - instrument_manager - INFO - find_matching_instrument:223 - Found matching instrument for NIFTY 26 JUN 25100 CALL: NIFTY 26 JUN 25100 CALL
2025-06-19 00:38:23,962 - instrument_manager - INFO - map_user_options:278 - Successfully mapped 2 instruments
2025-06-19 00:38:23,963 - instrument_manager - INFO - _save_mapped_instruments:293 - Saved mapped instruments to data\mapped_instruments.json
2025-06-19 00:38:23,963 - main - INFO - setup_instruments:117 - Successfully mapped 2 instruments
2025-06-19 00:38:23,964 - main - INFO - fetch_historical_data:140 - Starting historical data fetch...
2025-06-19 00:38:23,964 - historical_data - INFO - fetch_and_save_all_instruments:337 - Starting historical data fetch for 2 instruments
2025-06-19 00:38:23,964 - historical_data - INFO - fetch_and_save_all_instruments:347 - Processing 1/2: NIFTY 26 JUN 24500 PUT
2025-06-19 00:38:23,964 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62376, interval: 1min, from: 2025-05-20 00:38:23.964182, to: 2025-06-19 00:38:23.964182
2025-06-19 00:38:24,338 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:24,649 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-05-27&toDate=2025-06-03
2025-06-19 00:38:24,967 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-06-03&toDate=2025-06-10
2025-06-19 00:38:25,292 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-06-10&toDate=2025-06-17
2025-06-19 00:38:25,587 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=1&fromDate=2025-06-17&toDate=2025-06-19
2025-06-19 00:38:25,587 - historical_data - WARNING - fetch_historical_data:184 - No data fetched for 62376
2025-06-19 00:38:26,088 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62376, interval: 5min, from: 2025-05-20 00:38:26.088832, to: 2025-06-19 00:38:26.088832
2025-06-19 00:38:26,430 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=5&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:26,730 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=5&fromDate=2025-05-27&toDate=2025-06-03
2025-06-19 00:38:27,046 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=5&fromDate=2025-06-03&toDate=2025-06-10
2025-06-19 00:38:27,357 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=5&fromDate=2025-06-10&toDate=2025-06-17
2025-06-19 00:38:27,657 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=5&fromDate=2025-06-17&toDate=2025-06-19
2025-06-19 00:38:27,657 - historical_data - WARNING - fetch_historical_data:184 - No data fetched for 62376
2025-06-19 00:38:28,159 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62376, interval: 60min, from: 2025-05-20 00:38:28.159658, to: 2025-06-19 00:38:28.159658
2025-06-19 00:38:28,461 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=60&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:28,762 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=60&fromDate=2025-05-27&toDate=2025-06-03
2025-06-19 00:38:29,069 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=60&fromDate=2025-06-03&toDate=2025-06-10
2025-06-19 00:38:29,407 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=60&fromDate=2025-06-10&toDate=2025-06-17
2025-06-19 00:38:29,704 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62376: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62376&interval=60&fromDate=2025-06-17&toDate=2025-06-19
2025-06-19 00:38:29,704 - historical_data - WARNING - fetch_historical_data:184 - No data fetched for 62376
2025-06-19 00:38:30,206 - historical_data - INFO - fetch_and_save_all_instruments:347 - Processing 2/2: NIFTY 26 JUN 25100 CALL
2025-06-19 00:38:30,206 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62401, interval: 1min, from: 2025-05-20 00:38:30.206058, to: 2025-06-19 00:38:30.206058
2025-06-19 00:38:30,525 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62401: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=1&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:30,525 - historical_data - ERROR - fetch_and_save_all_instruments:366 - Failed to fetch 1min data for NIFTY 26 JUN 25100 CALL: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=1&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:30,526 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62401, interval: 5min, from: 2025-05-20 00:38:30.526170, to: 2025-06-19 00:38:30.526170
2025-06-19 00:38:30,825 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62401: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=5&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:30,825 - historical_data - ERROR - fetch_and_save_all_instruments:366 - Failed to fetch 5min data for NIFTY 26 JUN 25100 CALL: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=5&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:30,826 - historical_data - INFO - fetch_historical_data:104 - Fetching historical data for 62401, interval: 60min, from: 2025-05-20 00:38:30.826868, to: 2025-06-19 00:38:30.826868
2025-06-19 00:38:31,126 - historical_data - ERROR - fetch_historical_data:166 - API request failed for 62401: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=60&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:31,127 - historical_data - ERROR - fetch_and_save_all_instruments:366 - Failed to fetch 60min data for NIFTY 26 JUN 25100 CALL: 400 Client Error:  for url: https://api.dhan.co/v2/charts/intraday?instrumentId=62401&interval=60&fromDate=2025-05-20&toDate=2025-05-27
2025-06-19 00:38:31,127 - historical_data - INFO - fetch_and_save_all_instruments:376 - Historical data fetch completed. Processed 0 instruments successfully.
2025-06-19 00:38:31,128 - main - INFO - fetch_historical_data:157 - Historical data fetch completed: 0/2 instruments
