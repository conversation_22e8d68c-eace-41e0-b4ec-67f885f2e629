"""
WebSocket Real-time Data Streaming Module for DhanHQ Option Data Fetcher.

This module handles WebSocket connections for real-time tick data streaming
with authentication, heartbeat, and reconnection logic.
"""

import json
import time
import threading
import logging
from typing import Dict, List, Callable, Optional, Any
from datetime import datetime
import websocket
from queue import Queue, Empty

from .config import DhanConfig, LoggerSetup


class DhanWebSocketClient:
    """WebSocket client for DhanHQ real-time data streaming."""
    
    def __init__(self, config: DhanConfig, on_tick: Optional[Callable] = None):
        """
        Initialize WebSocket client.
        
        Args:
            config (DhanConfig): Configuration instance
            on_tick (Optional[Callable]): Callback function for tick data
        """
        self.config = config
        self.logger = LoggerSetup.setup_logger(config, "websocket_client")
        self.on_tick = on_tick
        
        # Connection state
        self.ws = None
        self.is_connected = False
        self.is_reconnecting = False
        self.reconnect_attempts = 0
        self.subscribed_instruments = set()
        
        # Threading
        self.heartbeat_thread = None
        self.reconnect_thread = None
        self.stop_event = threading.Event()
        
        # Data queue for thread-safe communication
        self.tick_queue = Queue()
        
        # Callbacks
        self.on_connect_callback = None
        self.on_disconnect_callback = None
        self.on_error_callback = None
        
    def connect(self) -> bool:
        """
        Establish WebSocket connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            self.logger.info("Connecting to DhanHQ WebSocket...")
            
            # Create WebSocket connection
            self.ws = websocket.WebSocketApp(
                self.config.WEBSOCKET_URL,
                header=self._get_auth_headers(),
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # Start connection in a separate thread
            self.ws_thread = threading.Thread(target=self.ws.run_forever)
            self.ws_thread.daemon = True
            self.ws_thread.start()
            
            # Wait for connection to establish
            timeout = 10
            start_time = time.time()
            while not self.is_connected and time.time() - start_time < timeout:
                time.sleep(0.1)
            
            if self.is_connected:
                self.logger.info("WebSocket connection established")
                self.reconnect_attempts = 0
                return True
            else:
                self.logger.error("WebSocket connection timeout")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to connect to WebSocket: {e}")
            return False
    
    def disconnect(self) -> None:
        """Disconnect from WebSocket."""
        self.logger.info("Disconnecting from WebSocket...")
        
        # Set stop event
        self.stop_event.set()
        
        # Stop heartbeat
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=2)
        
        # Close WebSocket
        if self.ws:
            self.ws.close()
        
        self.is_connected = False
        self.logger.info("WebSocket disconnected")
    
    def subscribe(self, instrument_tokens: List[str]) -> bool:
        """
        Subscribe to instrument tick data.
        
        Args:
            instrument_tokens (List[str]): List of instrument tokens to subscribe
            
        Returns:
            bool: True if subscription successful, False otherwise
        """
        if not self.is_connected:
            self.logger.error("Cannot subscribe: WebSocket not connected")
            return False
        
        try:
            subscription_message = {
                "action": "subscribe",
                "instruments": instrument_tokens
            }
            
            self.ws.send(json.dumps(subscription_message))
            self.subscribed_instruments.update(instrument_tokens)
            
            self.logger.info(f"Subscribed to {len(instrument_tokens)} instruments")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to subscribe to instruments: {e}")
            return False
    
    def unsubscribe(self, instrument_tokens: List[str]) -> bool:
        """
        Unsubscribe from instrument tick data.
        
        Args:
            instrument_tokens (List[str]): List of instrument tokens to unsubscribe
            
        Returns:
            bool: True if unsubscription successful, False otherwise
        """
        if not self.is_connected:
            self.logger.error("Cannot unsubscribe: WebSocket not connected")
            return False
        
        try:
            unsubscription_message = {
                "action": "unsubscribe",
                "instruments": instrument_tokens
            }
            
            self.ws.send(json.dumps(unsubscription_message))
            self.subscribed_instruments.difference_update(instrument_tokens)
            
            self.logger.info(f"Unsubscribed from {len(instrument_tokens)} instruments")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to unsubscribe from instruments: {e}")
            return False
    
    def _get_auth_headers(self) -> List[str]:
        """Get authentication headers for WebSocket connection."""
        return [
            f"Authorization: Bearer {self.config.access_token}",
            f"X-Client-Id: {self.config.client_id}"
        ]
    
    def _on_open(self, ws) -> None:
        """Handle WebSocket connection open event."""
        self.logger.info("WebSocket connection opened")
        self.is_connected = True
        self.is_reconnecting = False
        
        # Start heartbeat
        self._start_heartbeat()
        
        # Call user callback
        if self.on_connect_callback:
            try:
                self.on_connect_callback()
            except Exception as e:
                self.logger.error(f"Error in connect callback: {e}")
    
    def _on_message(self, ws, message: str) -> None:
        """
        Handle incoming WebSocket messages.
        
        Args:
            ws: WebSocket instance
            message (str): Received message
        """
        try:
            data = json.loads(message)
            
            # Handle different message types
            if data.get('type') == 'tick':
                self._handle_tick_data(data)
            elif data.get('type') == 'heartbeat':
                self.logger.debug("Received heartbeat response")
            elif data.get('type') == 'error':
                self.logger.error(f"WebSocket error: {data.get('message', 'Unknown error')}")
            else:
                self.logger.debug(f"Received message: {data}")
                
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse WebSocket message: {e}")
        except Exception as e:
            self.logger.error(f"Error handling WebSocket message: {e}")
    
    def _handle_tick_data(self, data: Dict[str, Any]) -> None:
        """
        Handle tick data from WebSocket.
        
        Args:
            data (Dict[str, Any]): Tick data
        """
        try:
            # Add timestamp if not present
            if 'timestamp' not in data:
                data['timestamp'] = datetime.now().isoformat()
            
            # Put tick data in queue for thread-safe processing
            self.tick_queue.put(data)
            
            # Call user callback
            if self.on_tick:
                try:
                    self.on_tick(data)
                except Exception as e:
                    self.logger.error(f"Error in tick callback: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error handling tick data: {e}")
    
    def _on_error(self, ws, error) -> None:
        """
        Handle WebSocket error.
        
        Args:
            ws: WebSocket instance
            error: Error object
        """
        self.logger.error(f"WebSocket error: {error}")
        
        # Call user callback
        if self.on_error_callback:
            try:
                self.on_error_callback(error)
            except Exception as e:
                self.logger.error(f"Error in error callback: {e}")
    
    def _on_close(self, ws, close_status_code, close_msg) -> None:
        """
        Handle WebSocket connection close.
        
        Args:
            ws: WebSocket instance
            close_status_code: Close status code
            close_msg: Close message
        """
        self.logger.warning(f"WebSocket connection closed: {close_status_code} - {close_msg}")
        self.is_connected = False
        
        # Stop heartbeat
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.stop_event.set()
        
        # Call user callback
        if self.on_disconnect_callback:
            try:
                self.on_disconnect_callback()
            except Exception as e:
                self.logger.error(f"Error in disconnect callback: {e}")
        
        # Attempt reconnection if not intentionally disconnected
        if not self.stop_event.is_set():
            self._attempt_reconnection()
    
    def _start_heartbeat(self) -> None:
        """Start heartbeat thread."""
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            return
        
        self.stop_event.clear()
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop)
        self.heartbeat_thread.daemon = True
        self.heartbeat_thread.start()
        
        self.logger.debug("Heartbeat thread started")
    
    def _heartbeat_loop(self) -> None:
        """Heartbeat loop to keep connection alive."""
        while not self.stop_event.is_set() and self.is_connected:
            try:
                if self.ws and self.is_connected:
                    heartbeat_message = {"action": "ping"}
                    self.ws.send(json.dumps(heartbeat_message))
                    self.logger.debug("Sent heartbeat ping")
                
                # Wait for heartbeat interval or stop event
                self.stop_event.wait(self.config.HEARTBEAT_INTERVAL)
                
            except Exception as e:
                self.logger.error(f"Error in heartbeat: {e}")
                break
        
        self.logger.debug("Heartbeat thread stopped")

    def _attempt_reconnection(self) -> None:
        """Attempt to reconnect with exponential backoff."""
        if self.is_reconnecting or self.stop_event.is_set():
            return

        self.is_reconnecting = True
        self.reconnect_attempts += 1

        if self.reconnect_attempts > self.config.MAX_RECONNECT_ATTEMPTS:
            self.logger.error("Maximum reconnection attempts reached. Giving up.")
            self.is_reconnecting = False
            return

        # Calculate backoff delay
        delay = min(
            self.config.RECONNECT_DELAY_MIN * (2 ** (self.reconnect_attempts - 1)),
            self.config.RECONNECT_DELAY_MAX
        )

        self.logger.info(f"Attempting reconnection {self.reconnect_attempts}/{self.config.MAX_RECONNECT_ATTEMPTS} in {delay} seconds")

        # Start reconnection thread
        self.reconnect_thread = threading.Thread(target=self._reconnect_with_delay, args=(delay,))
        self.reconnect_thread.daemon = True
        self.reconnect_thread.start()

    def _reconnect_with_delay(self, delay: float) -> None:
        """
        Reconnect after delay.

        Args:
            delay (float): Delay in seconds before reconnection
        """
        # Wait for delay or stop event
        if self.stop_event.wait(delay):
            self.is_reconnecting = False
            return

        try:
            # Attempt reconnection
            if self.connect():
                # Re-subscribe to instruments
                if self.subscribed_instruments:
                    self.subscribe(list(self.subscribed_instruments))
                self.logger.info("Reconnection successful")
            else:
                self.logger.warning("Reconnection failed")
                # Try again
                self._attempt_reconnection()

        except Exception as e:
            self.logger.error(f"Error during reconnection: {e}")
            self._attempt_reconnection()
        finally:
            self.is_reconnecting = False

    def get_tick_data(self, timeout: float = 1.0) -> Optional[Dict[str, Any]]:
        """
        Get tick data from queue.

        Args:
            timeout (float): Timeout in seconds

        Returns:
            Optional[Dict[str, Any]]: Tick data or None if timeout
        """
        try:
            return self.tick_queue.get(timeout=timeout)
        except Empty:
            return None

    def set_callbacks(
        self,
        on_connect: Optional[Callable] = None,
        on_disconnect: Optional[Callable] = None,
        on_error: Optional[Callable] = None,
        on_tick: Optional[Callable] = None
    ) -> None:
        """
        Set callback functions.

        Args:
            on_connect (Optional[Callable]): Called when connection is established
            on_disconnect (Optional[Callable]): Called when connection is lost
            on_error (Optional[Callable]): Called when an error occurs
            on_tick (Optional[Callable]): Called when tick data is received
        """
        self.on_connect_callback = on_connect
        self.on_disconnect_callback = on_disconnect
        self.on_error_callback = on_error
        if on_tick:
            self.on_tick = on_tick

    def is_connection_alive(self) -> bool:
        """
        Check if WebSocket connection is alive.

        Returns:
            bool: True if connection is alive, False otherwise
        """
        return self.is_connected and self.ws is not None

    def get_connection_status(self) -> Dict[str, Any]:
        """
        Get connection status information.

        Returns:
            Dict[str, Any]: Connection status details
        """
        return {
            'connected': self.is_connected,
            'reconnecting': self.is_reconnecting,
            'reconnect_attempts': self.reconnect_attempts,
            'subscribed_instruments': len(self.subscribed_instruments),
            'queue_size': self.tick_queue.qsize()
        }
