[{"timestamp": "2025-06-19T00:20:10.602957", "error_type": "configuration", "severity": "high", "message": "Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments", "context": {}, "exception_type": "HTTPError", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\main.py\", line 80, in setup_instruments\n    self.instrument_manager.download_instruments(force_refresh)\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 70, in download_instruments\n    response.raise_for_status()\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\requests\\models.py\", line 1021, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments\n"}, {"timestamp": "2025-06-19T00:22:09.824464", "error_type": "configuration", "severity": "high", "message": "Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments", "context": {}, "exception_type": "HTTPError", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\main.py\", line 80, in setup_instruments\n    self.instrument_manager.download_instruments(force_refresh)\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 70, in download_instruments\n    response.raise_for_status()\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\requests\\models.py\", line 1021, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments\n"}, {"timestamp": "2025-06-19T00:26:32.099644", "error_type": "configuration", "severity": "high", "message": "Failed to setup instruments: 404 Client Error:  for url: https://api.dhan.co/v2/instruments", "context": {}, "exception_type": "HTTPError", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\main.py\", line 80, in setup_instruments\n    self.instrument_manager.download_instruments(force_refresh)\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 70, in download_instruments\n    response.raise_for_status()\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\requests\\models.py\", line 1021, in raise_for_status\n    raise HTTPError(http_error_msg, response=self)\nrequests.exceptions.HTTPError: 404 Client Error:  for url: https://api.dhan.co/v2/instruments\n"}, {"timestamp": "2025-06-19T00:31:25.537073", "error_type": "configuration", "severity": "high", "message": "Failed to setup instruments: 'SEM_SMST_SECURITY_ID'", "context": {}, "exception_type": "KeyError", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py\", line 3790, in get_loc\n    return self._engine.get_loc(casted_key)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"index.pyx\", line 152, in pandas._libs.index.IndexEngine.get_loc\n  File \"index.pyx\", line 181, in pandas._libs.index.IndexEngine.get_loc\n  File \"pandas\\_libs\\hashtable_class_helper.pxi\", line 7080, in pandas._libs.hashtable.PyObjectHashTable.get_item\n  File \"pandas\\_libs\\hashtable_class_helper.pxi\", line 7088, in pandas._libs.hashtable.PyObjectHashTable.get_item\nKeyError: 'SEM_SMST_SECURITY_ID'\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\main.py\", line 86, in setup_instruments\n    self.mapped_instruments = self.instrument_manager.map_user_options()\n                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 260, in map_user_options\n    instrument = self.find_matching_instrument(parsed_option)\n                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\src\\instrument_manager.py\", line 193, in find_matching_instrument\n    symbol_filter = self.instruments_df['SEM_SMST_SECURITY_ID'].str.contains(\n                    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py\", line 3896, in __getitem__\n    indexer = self.columns.get_loc(key)\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"C:\\Users\\<USER>\\Documents\\Trading\\India trading strategies\\Option Trade_v1\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py\", line 3797, in get_loc\n    raise KeyError(key) from err\nKeyError: 'SEM_SMST_SECURITY_ID'\n"}]