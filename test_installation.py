#!/usr/bin/env python3
"""
Installation Test Script for DhanHQ Option Data Fetcher

This script tests the basic functionality and configuration
to ensure the application is properly installed and configured.
"""

import os
import sys
import traceback
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test if all required modules can be imported."""
    print("Testing imports...")
    
    try:
        import requests
        import pandas as pd
        import numpy as np
        import websocket
        from dotenv import load_dotenv
        print("✓ All required packages imported successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\nTesting configuration...")
    
    try:
        from config import get_config
        config = get_config()
        print("✓ Configuration loaded successfully")
        
        # Test if credentials are set (but don't print them)
        if config.client_id and config.access_token:
            print("✓ API credentials found in configuration")
        else:
            print("⚠ API credentials not found. Please check your .env file")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def test_directories():
    """Test if required directories exist or can be created."""
    print("\nTesting directories...")
    
    required_dirs = ['data', 'logs', 'data/historical', 'data/realtime']
    
    try:
        for directory in required_dirs:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
            print(f"✓ Directory exists: {directory}")
        return True
    except Exception as e:
        print(f"✗ Directory error: {e}")
        return False

def test_csv_file():
    """Test if user options CSV file exists and is readable."""
    print("\nTesting CSV file...")
    
    csv_file = "User_options_input.csv"
    
    try:
        if not os.path.exists(csv_file):
            print(f"⚠ CSV file not found: {csv_file}")
            return False
        
        with open(csv_file, 'r') as f:
            lines = f.readlines()
        
        if len(lines) < 2:
            print("⚠ CSV file appears to be empty or has no data rows")
            return False
        
        print(f"✓ CSV file found with {len(lines)-1} data rows")
        return True
    except Exception as e:
        print(f"✗ CSV file error: {e}")
        return False

def test_instrument_manager():
    """Test instrument manager functionality."""
    print("\nTesting instrument manager...")
    
    try:
        from config import get_config
        from instrument_manager import InstrumentManager
        
        config = get_config()
        manager = InstrumentManager(config)
        
        # Test CSV loading
        manager.load_user_options()
        print("✓ User options loaded successfully")
        
        # Test option name parsing
        test_option = "NIFTY 26 JUN 24500 CALL"
        parsed = manager.parse_option_name(test_option)
        
        if parsed:
            print(f"✓ Option name parsing works: {test_option}")
        else:
            print(f"⚠ Option name parsing failed for: {test_option}")
        
        return True
    except Exception as e:
        print(f"✗ Instrument manager error: {e}")
        return False

def test_data_processor():
    """Test data processor functionality."""
    print("\nTesting data processor...")
    
    try:
        from config import get_config
        from data_processor import DataProcessor, CandleAggregator
        
        config = get_config()
        processor = DataProcessor(config)
        
        # Test candle aggregator
        aggregator = CandleAggregator(1)  # 1-minute candles
        
        # Test with sample tick data
        sample_tick = {
            'price': 100.0,
            'volume': 10,
            'timestamp': datetime.now().isoformat()
        }
        
        result = aggregator.add_tick(sample_tick)
        print("✓ Data processor and candle aggregator working")
        
        return True
    except Exception as e:
        print(f"✗ Data processor error: {e}")
        return False

def test_error_handler():
    """Test error handler functionality."""
    print("\nTesting error handler...")
    
    try:
        from config import get_config
        from error_handler import ErrorHandler, ErrorType, ErrorSeverity
        
        config = get_config()
        error_handler = ErrorHandler(config)
        
        # Test error handling
        error_handler.handle_error(
            ErrorType.CONFIGURATION,
            ErrorSeverity.LOW,
            "Test error message",
            context={'test': True}
        )
        
        print("✓ Error handler working")
        return True
    except Exception as e:
        print(f"✗ Error handler error: {e}")
        return False

def main():
    """Run all tests."""
    print("DhanHQ Option Data Fetcher - Installation Test")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_directories,
        test_csv_file,
        test_instrument_manager,
        test_data_processor,
        test_error_handler
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        print("\nNext steps:")
        print("1. Ensure your .env file has valid DhanHQ credentials")
        print("2. Update User_options_input.csv with your desired options")
        print("3. Run: python main.py --full")
    else:
        print("⚠ Some tests failed. Please check the errors above.")
        print("\nCommon solutions:")
        print("1. Ensure all dependencies are installed: uv pip install -r requirements.txt")
        print("2. Check your .env file configuration")
        print("3. Verify file permissions for data and logs directories")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
