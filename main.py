#!/usr/bin/env python3
"""
DhanHQ Option Data Fetcher and Real-time Monitor

Main application script that orchestrates all components for fetching
historical data and streaming real-time option data from DhanHQ API.
"""

import os
import sys
import time
import signal
import argparse
import logging
from typing import Dict, List, Optional
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import get_config, LoggerSetup
from src.instrument_manager import InstrumentManager
from src.historical_data import HistoricalDataFetcher
from src.websocket_client import <PERSON>hanWebSocketClient
from src.data_processor import DataProcessor
from src.error_handler import <PERSON>rror<PERSON>and<PERSON>, ErrorType, ErrorSeverity


class DhanOptionMonitor:
    """Main application class for DhanHQ Option Data Fetcher."""
    
    def __init__(self, env_file: str = ".env"):
        """
        Initialize the option monitor.
        
        Args:
            env_file (str): Path to environment file
        """
        # Load configuration
        self.config = get_config(env_file)
        self.logger = LoggerSetup.setup_logger(self.config, "main")
        
        # Initialize components
        self.error_handler = ErrorHandler(self.config)
        self.instrument_manager = InstrumentManager(self.config)
        self.historical_fetcher = HistoricalDataFetcher(self.config)
        self.data_processor = DataProcessor(self.config)
        self.websocket_client = None
        
        # State
        self.is_running = False
        self.mapped_instruments = []
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("DhanHQ Option Monitor initialized")

    def _load_mapped_instruments(self) -> bool:
        """
        Load mapped instruments from JSON file.

        Returns:
            bool: True if loaded successfully, False otherwise
        """
        import json
        mapped_file = os.path.join(self.config.data_dir, "mapped_instruments.json")

        if not os.path.exists(mapped_file):
            self.logger.warning("No mapped instruments file found")
            return False

        try:
            with open(mapped_file, 'r', encoding='utf-8') as f:
                self.mapped_instruments = json.load(f)

            self.logger.info(f"Loaded {len(self.mapped_instruments)} mapped instruments from file")
            return True

        except Exception as e:
            self.logger.error(f"Failed to load mapped instruments: {e}")
            return False

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}, shutting down...")
        self.stop()
    
    def setup_instruments(self, csv_file: str = "User_options_input.csv", force_refresh: bool = False) -> bool:
        """
        Setup instruments by downloading instrument list and mapping user options.
        
        Args:
            csv_file (str): Path to user options CSV file
            force_refresh (bool): Force refresh of instrument data
            
        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            self.logger.info("Setting up instruments...")
            
            # Download instruments
            self.instrument_manager.download_instruments(force_refresh)
            
            # Load user options
            self.instrument_manager.load_user_options(csv_file)
            
            # Map instruments
            self.mapped_instruments = self.instrument_manager.map_user_options()
            
            if not self.mapped_instruments:
                self.logger.error("No instruments mapped successfully")
                return False
            
            self.logger.info(f"Successfully mapped {len(self.mapped_instruments)} instruments")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(
                ErrorType.CONFIGURATION,
                ErrorSeverity.HIGH,
                f"Failed to setup instruments: {e}",
                exception=e
            )
            return False
    
    def fetch_historical_data(self, intervals: Optional[List[int]] = None) -> bool:
        """
        Fetch historical data for all mapped instruments.

        Args:
            intervals (Optional[List[int]]): Timeframes to fetch (default: all supported)

        Returns:
            bool: True if fetch successful, False otherwise
        """
        try:
            self.logger.info("Starting historical data fetch...")

            # Load mapped instruments if not already loaded
            if not self.mapped_instruments:
                self._load_mapped_instruments()

            if not self.mapped_instruments:
                self.logger.error("No mapped instruments. Run setup_instruments() first.")
                return False
            
            results = self.historical_fetcher.fetch_and_save_all_instruments(
                self.mapped_instruments, intervals
            )
            
            success_count = len(results)
            total_count = len(self.mapped_instruments)
            
            self.logger.info(f"Historical data fetch completed: {success_count}/{total_count} instruments")
            return success_count > 0
            
        except Exception as e:
            self.error_handler.handle_error(
                ErrorType.DATA_PROCESSING,
                ErrorSeverity.HIGH,
                f"Failed to fetch historical data: {e}",
                exception=e
            )
            return False
    
    def start_realtime_monitoring(self) -> bool:
        """
        Start real-time data monitoring via WebSocket.
        
        Returns:
            bool: True if monitoring started successfully, False otherwise
        """
        try:
            self.logger.info("Starting real-time monitoring...")

            # Load mapped instruments if not already loaded
            if not self.mapped_instruments:
                self._load_mapped_instruments()

            if not self.mapped_instruments:
                self.logger.error("No mapped instruments. Run setup_instruments() first.")
                return False
            
            # Initialize WebSocket client
            self.websocket_client = DhanWebSocketClient(
                self.config,
                on_tick=self._handle_tick_data
            )
            
            # Set up error recovery callbacks
            self._setup_error_recovery()
            
            # Start data processor
            self.data_processor.start_processing()
            
            # Add instruments to processor
            for instrument in self.mapped_instruments:
                self.data_processor.add_instrument(
                    instrument['instrument_token'],
                    instrument['display_name']
                )
            
            # Connect to WebSocket
            if not self.websocket_client.connect():
                self.logger.error("Failed to connect to WebSocket")
                return False
            
            # Subscribe to instruments
            instrument_tokens = self.instrument_manager.get_instrument_tokens()
            if not self.websocket_client.subscribe(instrument_tokens):
                self.logger.error("Failed to subscribe to instruments")
                return False
            
            self.is_running = True
            self.logger.info("Real-time monitoring started successfully")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(
                ErrorType.WEBSOCKET,
                ErrorSeverity.CRITICAL,
                f"Failed to start real-time monitoring: {e}",
                exception=e
            )
            return False
    
    def _handle_tick_data(self, tick_data: Dict) -> None:
        """
        Handle incoming tick data from WebSocket.
        
        Args:
            tick_data (Dict): Tick data from WebSocket
        """
        try:
            # Add tick data to processor
            self.data_processor.add_tick_data(tick_data)
            
        except Exception as e:
            self.error_handler.handle_error(
                ErrorType.DATA_PROCESSING,
                ErrorSeverity.MEDIUM,
                f"Error handling tick data: {e}",
                context={'tick_data': tick_data},
                exception=e
            )
    
    def _setup_error_recovery(self) -> None:
        """Setup error recovery callbacks."""
        def websocket_recovery(error_record):
            """Recovery callback for WebSocket errors."""
            if self.websocket_client and not self.websocket_client.is_connection_alive():
                self.logger.info("Attempting WebSocket reconnection...")
                return self.websocket_client.connect()
            return True
        
        self.error_handler.register_recovery_callback(ErrorType.WEBSOCKET, websocket_recovery)
    
    def run_monitoring_loop(self) -> None:
        """Run the main monitoring loop."""
        self.logger.info("Starting monitoring loop...")
        
        try:
            while self.is_running:
                # Check connection status
                if self.websocket_client and not self.websocket_client.is_connection_alive():
                    self.logger.warning("WebSocket connection lost")
                    self.error_handler.handle_error(
                        ErrorType.WEBSOCKET,
                        ErrorSeverity.HIGH,
                        "WebSocket connection lost"
                    )
                
                # Log statistics periodically
                self._log_statistics()
                
                # Sleep for a short interval
                time.sleep(10)
                
        except KeyboardInterrupt:
            self.logger.info("Monitoring interrupted by user")
        except Exception as e:
            self.error_handler.handle_error(
                ErrorType.UNKNOWN,
                ErrorSeverity.CRITICAL,
                f"Unexpected error in monitoring loop: {e}",
                exception=e
            )
        finally:
            self.stop()
    
    def _log_statistics(self) -> None:
        """Log system statistics."""
        try:
            # Data processor stats
            processor_stats = self.data_processor.get_statistics()
            
            # WebSocket stats
            ws_stats = {}
            if self.websocket_client:
                ws_stats = self.websocket_client.get_connection_status()
            
            # Error stats
            error_stats = self.error_handler.get_error_statistics()
            
            self.logger.info(
                f"Stats - Ticks: {processor_stats['ticks_processed']}, "
                f"Candles: {processor_stats['candles_created']}, "
                f"Files: {processor_stats['files_updated']}, "
                f"Errors: {error_stats['total_errors']}, "
                f"WS Connected: {ws_stats.get('connected', False)}"
            )
            
        except Exception as e:
            self.logger.error(f"Error logging statistics: {e}")
    
    def stop(self) -> None:
        """Stop the monitoring system."""
        self.logger.info("Stopping DhanHQ Option Monitor...")
        
        self.is_running = False
        
        # Stop WebSocket
        if self.websocket_client:
            self.websocket_client.disconnect()
        
        # Stop data processor
        self.data_processor.stop_processing()
        
        # Clean up old data
        self.data_processor.clear_old_data()
        self.error_handler.clear_old_errors()
        
        self.logger.info("DhanHQ Option Monitor stopped")
    
    def get_status(self) -> Dict:
        """
        Get current system status.
        
        Returns:
            Dict: System status information
        """
        status = {
            'running': self.is_running,
            'mapped_instruments': len(self.mapped_instruments),
            'websocket_connected': False,
            'processor_stats': {},
            'error_stats': {}
        }
        
        if self.websocket_client:
            status['websocket_connected'] = self.websocket_client.is_connection_alive()
        
        if self.data_processor:
            status['processor_stats'] = self.data_processor.get_statistics()
        
        if self.error_handler:
            status['error_stats'] = self.error_handler.get_error_statistics()
        
        return status


def create_cli_parser() -> argparse.ArgumentParser:
    """Create command-line interface parser."""
    parser = argparse.ArgumentParser(
        description="DhanHQ Option Data Fetcher and Real-time Monitor",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --setup-only                    # Setup instruments only
  python main.py --historical-only               # Fetch historical data only
  python main.py --realtime-only                 # Start real-time monitoring only
  python main.py --full                          # Full workflow (setup + historical + realtime)
  python main.py --csv custom_options.csv        # Use custom options file
  python main.py --env custom.env                # Use custom environment file
        """
    )

    # Mode selection
    mode_group = parser.add_mutually_exclusive_group()
    mode_group.add_argument(
        '--setup-only',
        action='store_true',
        help='Setup instruments only (download and map)'
    )
    mode_group.add_argument(
        '--historical-only',
        action='store_true',
        help='Fetch historical data only'
    )
    mode_group.add_argument(
        '--realtime-only',
        action='store_true',
        help='Start real-time monitoring only'
    )
    mode_group.add_argument(
        '--full',
        action='store_true',
        help='Run full workflow (setup + historical + realtime)'
    )

    # Configuration options
    parser.add_argument(
        '--csv',
        default='User_options_input.csv',
        help='Path to user options CSV file (default: User_options_input.csv)'
    )
    parser.add_argument(
        '--env',
        default='.env',
        help='Path to environment file (default: .env)'
    )
    parser.add_argument(
        '--force-refresh',
        action='store_true',
        help='Force refresh of instrument data'
    )

    # Historical data options
    parser.add_argument(
        '--intervals',
        nargs='+',
        type=int,
        choices=[1, 5, 60],
        help='Timeframes to fetch in minutes (default: all supported)'
    )

    # Logging options
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
        help='Override log level'
    )

    # Status and utility options
    parser.add_argument(
        '--status',
        action='store_true',
        help='Show current system status and exit'
    )
    parser.add_argument(
        '--version',
        action='version',
        version='DhanHQ Option Monitor v1.0.0'
    )

    return parser


def main():
    """Main entry point."""
    parser = create_cli_parser()
    args = parser.parse_args()

    # Override log level if specified
    if args.log_level:
        os.environ['LOG_LEVEL'] = args.log_level

    try:
        # Initialize monitor
        monitor = DhanOptionMonitor(args.env)

        # Handle status request
        if args.status:
            status = monitor.get_status()
            print("\n=== DhanHQ Option Monitor Status ===")
            print(f"Running: {status['running']}")
            print(f"Mapped Instruments: {status['mapped_instruments']}")
            print(f"WebSocket Connected: {status['websocket_connected']}")

            if status['processor_stats']:
                stats = status['processor_stats']
                print(f"Ticks Processed: {stats.get('ticks_processed', 0)}")
                print(f"Candles Created: {stats.get('candles_created', 0)}")
                print(f"Files Updated: {stats.get('files_updated', 0)}")

            if status['error_stats']:
                error_stats = status['error_stats']
                print(f"Total Errors: {error_stats.get('total_errors', 0)}")
                print(f"Errors Last Hour: {error_stats.get('errors_last_hour', 0)}")

            return 0

        # Determine mode
        if not any([args.setup_only, args.historical_only, args.realtime_only, args.full]):
            # Default to full workflow
            args.full = True

        success = True

        # Setup instruments
        if args.setup_only or args.full:
            print("Setting up instruments...")
            success = monitor.setup_instruments(args.csv, args.force_refresh)
            if not success:
                print("ERROR: Failed to setup instruments")
                return 1
            print("✓ Instruments setup completed")

        # Fetch historical data
        if (args.historical_only or args.full) and success:
            print("Fetching historical data...")
            success = monitor.fetch_historical_data(args.intervals)
            if not success:
                print("ERROR: Failed to fetch historical data")
                return 1
            print("✓ Historical data fetch completed")

        # Start real-time monitoring
        if (args.realtime_only or args.full) and success:
            print("Starting real-time monitoring...")
            success = monitor.start_realtime_monitoring()
            if not success:
                print("ERROR: Failed to start real-time monitoring")
                return 1
            print("✓ Real-time monitoring started")

            # Run monitoring loop
            print("\nMonitoring active. Press Ctrl+C to stop.")
            monitor.run_monitoring_loop()

        return 0

    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        return 0
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
        logging.exception("Critical error in main")
        return 1


if __name__ == "__main__":
    sys.exit(main())
